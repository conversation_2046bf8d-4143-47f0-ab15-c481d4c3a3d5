import{d,h as a,ab as O,ac as m,ad as y}from"./index-CTFw1yDv.js";function o(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?o(Object(r),!0).forEach(function(t){y(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var L={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M11.998 1.99561L22.998 3.09561V7.90558L20.0663 8.19876L21.0737 22.0006H13.9122L15.0077 8.71261L11.998 9L9.08393 8.71417L10.0755 22.0006H2.92239L3.92983 8.19876L0.998047 7.90558V3.09561L11.998 1.99561ZM5.92062 8.39784L5.0737 20.0006H7.92064L7.06329 8.5121L5.92062 8.39784ZM17.0312 8.51068L16.0839 20.0006H18.9224L18.0757 8.4007C17.7242 8.4376 17.3788 8.47436 17.0312 8.51068Z"}}]},b=d({name:"ShimenFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:c}=O(t),p=a(()=>["t-icon","t-icon-shimen-filled",l.value]),u=a(()=>s(s({},c.value),r.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:v})}}));return()=>m(L,f.value)}});export{b as default};
