import{d as v,h as a,ab as d,ac as O,ad as m}from"./index-CTFw1yDv.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){m(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var y={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M1 4C1 2.34315 2.34315 1 4 1C5.30622 1 6.41746 1.83481 6.82929 3H17.1707C17.5825 1.83481 18.6938 1 20 1C21.6569 1 23 2.34315 23 4C23 5.30622 22.1652 6.41746 21 6.82929V17.1707C22.1652 17.5825 23 18.6938 23 20C23 21.6569 21.6569 23 20 23C18.6938 23 17.5825 22.1652 17.1707 21H6.82929C6.41746 22.1652 5.30622 23 4 23C2.34315 23 1 21.6569 1 20C1 18.6938 1.83481 17.5825 3 17.1707V6.82929C1.83481 6.41746 1 5.30622 1 4ZM5 6.82929V17.1707C5.85241 17.472 6.52801 18.1476 6.82929 19H17.1707C17.472 18.1476 18.1476 17.472 19 17.1707V6.82929C18.1476 6.52801 17.472 5.85241 17.1707 5H6.82929C6.52801 5.85241 5.85241 6.52801 5 6.82929Z"}}]},g=v({name:"TransformFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:c}=d(t),p=a(()=>["t-icon","t-icon-transform-filled",l.value]),f=a(()=>s(s({},c.value),r.style)),u=a(()=>({class:p.value,style:f.value,onClick:C=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:C})}}));return()=>O(y,u.value)}});export{g as default};
