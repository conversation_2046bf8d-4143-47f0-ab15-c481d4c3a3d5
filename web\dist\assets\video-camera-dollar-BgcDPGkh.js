import{d,h as n,ab as O,ac as m,ad as y}from"./index-CTFw1yDv.js";function i(e,a){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);a&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function c(e){for(var a=1;a<arguments.length;a++){var r=arguments[a]!=null?arguments[a]:{};a%2?i(Object(r),!0).forEach(function(t){y(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var L={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M6.55651e-07 3.99805L17 3.99805L17 8.43186L24 4.23186V19.7212L17 15.7212V19.998H0L6.55651e-07 3.99805ZM17 13.4177L22 16.2749V7.76423L17 10.7642V13.4177ZM2 5.99805L2 17.998L15 17.998L15 5.99805L2 5.99805ZM9.50004 7V8H11.5V10H7.50004V11L9.50004 11C10.6046 11 11.5 11.8954 11.5 13V14C11.5 15.1046 10.6046 16 9.50004 16L9.50004 17L7.50004 17L7.50004 16L5.50004 16V14H9.50004V13H7.50004C6.39548 13 5.50004 12.1046 5.50004 11V10C5.50004 8.89543 6.39547 8 7.50004 8V7H9.50004Z"}}]},V=d({name:"VideoCameraDollarIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,a){var{attrs:r}=a,t=n(()=>e.size),{className:o,style:s}=O(t),p=n(()=>["t-icon","t-icon-video-camera-dollar",o.value]),u=n(()=>c(c({},s.value),r.style)),v=n(()=>({class:p.value,style:u.value,onClick:f=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:f})}}));return()=>m(L,v.value)}});export{V as default};
