import{R as s}from"./index-BiCzxYwG.js";import{d as o,f as r,b as i,o as p,n,g as l,p as u,t as m}from"./index-R826otwI.js";const c={name:"ResultMaintenance"},b=o({...c,setup(f){return(t,e)=>{const a=r("t-button");return p(),i(s,{title:t.t("pages.result.maintenance.title"),tip:t.t("pages.result.maintenance.subtitle"),type:"maintenance"},{default:n(()=>[l(a,{theme:"primary",onClick:e[0]||(e[0]=()=>t.$router.push("/"))},{default:n(()=>[u(m(t.t("pages.result.maintenance.back")),1)]),_:1})]),_:1},8,["title","tip"])}}});export{b as default};
