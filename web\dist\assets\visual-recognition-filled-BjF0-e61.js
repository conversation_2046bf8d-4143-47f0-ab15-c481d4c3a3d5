import{d,h as a,ab as O,ac as g,ad as y}from"./index-R826otwI.js";function o(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?o(Object(t),!0).forEach(function(r){y(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):o(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M22 2H2V22H14.8762C14.0139 20.897 13.5 19.5085 13.5 18C13.5 16.61 13.9363 15.3219 14.6794 14.2652L9 8.58579L4 13.5858V4H20V11.5C20.6978 11.5 21.3699 11.61 22 11.8135V2Z"}},{tag:"path",attrs:{fill:"currentColor",d:"M16 5.25C14.4812 5.25 13.25 6.48122 13.25 8 13.25 9.51878 14.4812 10.75 16 10.75 17.5188 10.75 18.75 9.51878 18.75 8 18.75 6.48122 17.5188 5.25 16 5.25zM24 16V14H16V16H19V23H21V16H24z"}}]},C=d({name:"VisualRecognitionFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:i,style:c}=O(r),p=a(()=>["t-icon","t-icon-visual-recognition-filled",i.value]),u=a(()=>s(s({},c.value),t.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:f})}}));return()=>g(m,v.value)}});export{C as default};
