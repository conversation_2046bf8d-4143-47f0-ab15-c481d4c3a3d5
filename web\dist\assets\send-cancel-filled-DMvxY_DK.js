import{d as v,h as a,ab as L,ac as O,ad as y}from"./index-R826otwI.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function c(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){y(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var g={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M24.0029 12.0001L0.291992 1.66455L3.58686 11H11.0005V13H3.5869L0.291992 22.3356L12.8617 16.8565C13.7475 14.3198 16.1612 12.5 19 12.5C19.9274 12.5 20.8094 12.6942 21.6076 13.0442L24.0029 12.0001Z"}},{tag:"path",attrs:{fill:"currentColor",d:"M21.8308 14.7573L19.0024 17.5858L16.174 14.7573L14.7598 16.1715L17.5882 19L14.7598 21.8284L16.174 23.2426L19.0024 20.4142L21.8308 23.2426L23.245 21.8284L20.4166 19L23.245 16.1715L21.8308 14.7573Z"}}]},b=v({name:"SendCancelFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:l,style:s}=L(r),p=a(()=>["t-icon","t-icon-send-cancel-filled",l.value]),u=a(()=>c(c({},s.value),t.style)),f=a(()=>({class:p.value,style:u.value,onClick:d=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:d})}}));return()=>O(g,f.value)}});export{b as default};
