import{d as O,h as a,ab as y,ac as d,ad as L}from"./index-R826otwI.js";function l(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?l(Object(t),!0).forEach(function(r){L(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):l(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M12 1.91406L15.6428 10.6007L16.0488 11.4997L16.0244 11.5107L19.165 18.9999H21V20.9999H3V18.9999H4.83498L7.97562 11.5107L7.95124 11.4997L8.35724 10.6006L12 1.91406ZM10.1489 11.4999L9.00373 14.2306H14.9963L13.8511 11.4999H10.1489ZM13.0124 9.49987L12 7.08567L10.9876 9.49987H13.0124ZM15.835 16.2306H8.16502L7.00373 18.9999H16.9963L15.835 16.2306Z"}}]},g=O({name:"TrafficEventsIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:o,style:c}=y(r),p=a(()=>["t-icon","t-icon-traffic-events",o.value]),f=a(()=>s(s({},c.value),t.style)),u=a(()=>({class:p.value,style:f.value,onClick:v=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:v})}}));return()=>d(m,u.value)}});export{g as default};
