import{d as f,h as a,ab as O,ac as y,ad as d}from"./index-CTFw1yDv.js";function l(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function i(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?l(Object(r),!0).forEach(function(t){d(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var L={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M2 2L22 2L22 16H20L20 4L4 4L4 16H2L2 2ZM5.40267 9H8.22548L12 12.797L15.7745 9L18.5973 9L13.4107 14.2161L16.0985 16.92C16.5771 16.6523 17.1291 16.5 17.7143 16.5C19.514 16.5 21 17.9403 21 19.75C21 21.5597 19.514 23 17.7143 23C15.9145 23 14.4286 21.5597 14.4286 19.75C14.4286 19.26 14.5375 18.7971 14.7321 18.3825L12 15.6348L9.26788 18.3825C9.46249 18.7971 9.57143 19.26 9.57143 19.75C9.57143 21.5597 8.08546 23 6.28572 23C4.48597 23 3 21.5597 3 19.75C3 17.9403 4.48597 16.5 6.28571 16.5C6.87091 16.5 7.42293 16.6523 7.90149 16.92L10.5893 14.2161L5.40267 9ZM17.7143 18.5C16.9893 18.5 16.4286 19.0744 16.4286 19.75C16.4286 20.4256 16.9893 21 17.7143 21C18.4393 21 19 20.4256 19 19.75C19 19.0744 18.4393 18.5 17.7143 18.5ZM6.28571 18.5C5.56073 18.5 5 19.0744 5 19.75C5 20.4256 5.56073 21 6.28572 21C7.0107 21 7.57143 20.4256 7.57143 19.75C7.57143 19.0744 7.0107 18.5 6.28571 18.5Z"}}]},m=f({name:"ScreenshotIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:o,style:c}=O(t),p=a(()=>["t-icon","t-icon-screenshot",o.value]),u=a(()=>i(i({},c.value),r.style)),C=a(()=>({class:p.value,style:u.value,onClick:v=>{var s;return(s=e.onClick)===null||s===void 0?void 0:s.call(e,{e:v})}}));return()=>y(L,C.value)}});export{m as default};
