import{d as f,h as a,ab as O,ac as y,ad as d}from"./index-CTFw1yDv.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){d(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var V={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M3.27924 2H20.7208L21.6921 4.91402C21.8731 5.45691 22.083 6.24588 21.8555 7.06828C21.6913 7.66173 21.3946 8.19867 21 8.6458V20H22V22H2V20H3V8.64575C2.37852 7.94132 2 7.01438 2 6V5.83772L3.27924 2ZM5 9.87377V20H8V13H16V20H19V9.87377C18.6802 9.95615 18.3451 10 18 10C16.8053 10 15.7329 9.47624 15 8.64582C14.2671 9.47624 13.1947 10 12 10C10.8053 10 9.73295 9.47624 9 8.64582C8.26706 9.47624 7.19469 10 6 10C5.65489 10 5.31975 9.95615 5 9.87377ZM10 6C10 7.10457 10.8954 8 12 8C13.1046 8 14 7.10457 14 6V4H10V6ZM8 4H4.72076L4.00526 6.14649C4.04369 6.67948 4.29033 7.15392 4.66691 7.49097C5.02142 7.80826 5.48712 8 6 8C7.10457 8 8 7.10457 8 6V4ZM16 4V6C16 7.10457 16.8954 8 18 8C18.5129 8 18.9786 7.80826 19.3331 7.49097C19.6157 7.23801 19.8249 6.90744 19.9279 6.53505C19.987 6.32154 19.9537 6.02326 19.7947 5.54648L19.2792 4H16ZM14 20V15H10V20H14Z"}}]},m=f({name:"Shop5Icon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:o,style:c}=O(t),p=a(()=>["t-icon","t-icon-shop-5",o.value]),u=a(()=>s(s({},c.value),r.style)),C=a(()=>({class:p.value,style:u.value,onClick:v=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:v})}}));return()=>y(V,C.value)}});export{m as default};
