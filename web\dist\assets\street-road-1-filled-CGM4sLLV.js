import{d,h as a,ab as O,ac as y,ad as m}from"./index-R826otwI.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){m(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var b={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M22 2V22H2V2H22ZM7.7844 18.1766L10.1766 6.21547L8.21547 5.82324L5.82324 17.7844L7.7844 18.1766ZM18.1766 17.7844L15.7844 5.82324L13.8232 6.21547L16.2155 18.1766L18.1766 17.7844ZM12.9999 9.99994V6.99994H10.9999V9.99994H12.9999ZM12.9999 13.9999V10.9999H10.9999V13.9999H12.9999ZM12.9999 17.9999V14.9999H10.9999V17.9999H12.9999Z"}}]},P=d({name:"StreetRoad1FilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:l,style:c}=O(r),p=a(()=>["t-icon","t-icon-street-road-1-filled",l.value]),u=a(()=>s(s({},c.value),t.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>y(b,f.value)}});export{P as default};
