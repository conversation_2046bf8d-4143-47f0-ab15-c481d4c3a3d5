import{d,h as a,ab as O,ac as y,ad as C}from"./index-R826otwI.js";function l(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),t.push.apply(t,r)}return t}function c(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?l(Object(t),!0).forEach(function(r){C(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):l(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M4 8C4 3.58172 7.58172 0 12 0C16.4183 0 20 3.58172 20 8V12.6972L22 15.6972V20H16.3885C15.9338 22.004 14.1416 23.5 12 23.5C9.85841 23.5 8.06624 22.004 7.61151 20H2V15.6972L4 12.6972V8ZM9.70802 20C10.0938 20.883 10.9748 21.5 12 21.5C13.0252 21.5 13.9062 20.883 14.292 20H9.70802ZM12 2C8.68629 2 6 4.68629 6 8V13.3028L4 16.3028V18H20V16.3028L18 13.3028V8C18 4.68629 15.3137 2 12 2ZM13 6L13 9H16V11H13V14H11V11L8 11L8 9L11 9L11 6L13 6Z"}}]},g=d({name:"NotificationAddIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:o,style:s}=O(r),p=a(()=>["t-icon","t-icon-notification-add",o.value]),u=a(()=>c(c({},s.value),t.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:v})}}));return()=>y(m,f.value)}});export{g as default};
