import{d as O,h as a,ab as y,ac as d,ad as g}from"./index-R826otwI.js";function s(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,t)}return r}function l(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?s(Object(r),!0).forEach(function(t){g(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M13 3V6H11V3H13ZM20.4853 6.92848L18.364 9.0498L16.9497 7.63559L19.0711 5.51426L20.4853 6.92848ZM4.9289 5.51431L7.05022 7.63563L5.63601 9.04984L3.51469 6.92852L4.9289 5.51431ZM12 10C9.79086 10 8 11.7909 8 14V15H6V14C6 10.6863 8.68629 8 12 8C15.3137 8 18 10.6863 18 14V15H16V14C16 11.7909 14.2091 10 12 10ZM1 13H4V15H1V13ZM20 13H23V15H20V13ZM12 15.7981L15.3028 18H23V20H14.6972L12 18.2018L9.30278 20H1V18H8.69722L12 15.7981Z"}}]},H=O({name:"SunRisingIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:i,style:c}=y(t),p=a(()=>["t-icon","t-icon-sun-rising",i.value]),u=a(()=>l(l({},c.value),r.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:f})}}));return()=>d(m,v.value)}});export{H as default};
