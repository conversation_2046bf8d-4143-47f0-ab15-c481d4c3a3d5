import{d as O,h as a,ab as y,ac as b,ad as d}from"./index-R826otwI.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){d(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var L={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M22 2L2 2L2 22H22V2ZM20 16.5V20H4L4 16.5L7.66918 16.5C8.53303 17.9934 10.148 19 12 19C13.852 19 15.467 17.9934 16.3308 16.5H20ZM4 14.5L4 4L20 4V14.5H15.0352L14.7823 15.1248C14.3365 16.2261 13.2574 17 12 17C10.7426 17 9.66348 16.2261 9.2177 15.1248L8.96479 14.5L4 14.5ZM12 5.08579L7.58579 9.5L9 10.9142L11 8.91421V14H13V8.91421L15 10.9142L16.4142 9.5L12 5.08579Z"}}]},g=O({name:"OutboxIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:o,style:c}=y(r),p=a(()=>["t-icon","t-icon-outbox",o.value]),u=a(()=>s(s({},c.value),t.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:f})}}));return()=>b(L,v.value)}});export{g as default};
