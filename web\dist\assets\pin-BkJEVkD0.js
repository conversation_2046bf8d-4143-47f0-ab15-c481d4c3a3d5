import{d as L,h as a,ab as O,ac as y,ad as d}from"./index-R826otwI.js";function l(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?l(Object(r),!0).forEach(function(t){d(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M17.7874 0.693359L23.3095 6.21545L16.9447 13.9886L19.1329 16.1768L13.474 21.8358L8.52427 16.8861L2.16031 23.25L0.746094 21.8358L7.11005 15.4719L2.16024 10.5221L7.8192 4.86311L10.0081 7.05199L17.7874 0.693359ZM13.474 19.0073L16.3045 16.1768L14.2502 14.1225L20.6149 6.34933L17.6524 3.3868L9.87309 9.74543L7.8192 7.69154L4.98867 10.5221L13.474 19.0073Z"}}]},g=L({name:"PinIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:o,style:c}=O(t),p=a(()=>["t-icon","t-icon-pin",o.value]),u=a(()=>s(s({},c.value),r.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:f})}}));return()=>y(m,v.value)}});export{g as default};
