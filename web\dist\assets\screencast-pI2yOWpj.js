import{d as O,h as a,ab as y,ac as L,ad as d}from"./index-R826otwI.js";function s(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function l(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?s(Object(r),!0).forEach(function(t){d(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M1 1.99951L23.0001 1.99951L23.0001 18.9995H17.0001V16.9995L21.0001 16.9995L21.0001 3.99951L3.00013 3.99951L3.00099 16.9995L7.00006 16.9995L7.00007 18.9995L1.00112 18.9995L1 1.99951ZM5.58581 21.9999L12 15.5853L18.4142 21.9994L5.58581 21.9999ZM10.4142 19.9997L13.5859 19.9996L12.0001 18.4137L10.4142 19.9997Z"}}]},g=O({name:"ScreencastIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:o,style:i}=y(t),p=a(()=>["t-icon","t-icon-screencast",o.value]),u=a(()=>l(l({},i.value),r.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var c;return(c=e.onClick)===null||c===void 0?void 0:c.call(e,{e:f})}}));return()=>L(m,v.value)}});export{g as default};
