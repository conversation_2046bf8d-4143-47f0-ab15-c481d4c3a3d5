import{d,h as a,ab as C,ac as O,ad as y}from"./index-CTFw1yDv.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){y(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var h={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M2 12C2 16.1271 4.53351 19.0117 6.89576 20.8031C8.08675 21.7062 9.27184 22.3623 10.1566 22.7924C10.6002 23.008 10.9717 23.1685 11.2352 23.2761C11.3671 23.33 11.4722 23.3707 11.546 23.3985C11.6592 23.4412 11.7735 23.4808 11.8878 23.5205C11.9252 23.5335 11.9626 23.5464 12 23.5595C12.0374 23.5464 12.0749 23.5334 12.1124 23.5204C12.2267 23.4808 12.3409 23.4412 12.454 23.3985C12.5278 23.3707 12.6329 23.33 12.7648 23.2761C13.0283 23.1685 13.3998 23.008 13.8434 22.7924C14.7282 22.3623 15.9133 21.7062 17.1042 20.8031C19.4665 19.0117 22 16.1271 22 12V3.94055L12 0.440552L2 3.94055V12ZM13 8.5H10.9961V6.49609H13L13 8.5ZM11 16.5V10H13V16.5H11Z"}}]},b=d({name:"ShieldErrorFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:c}=C(t),p=a(()=>["t-icon","t-icon-shield-error-filled",l.value]),u=a(()=>s(s({},c.value),r.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>O(h,f.value)}});export{b as default};
