import{d as f,h as a,ab as O,ac as y,ad as g}from"./index-CTFw1yDv.js";function l(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?l(Object(r),!0).forEach(function(t){g(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M19 2.58569L23.4142 6.99991L22 8.41412L20 6.41412L20 19.9999H18L18 6.41412L16 8.41412L14.5858 6.99991L19 2.58569ZM2 3.99991H13V5.99991H2V3.99991ZM2 10.9999H15V12.9999H2V10.9999ZM2 17.9999H15V19.9999H2V17.9999Z"}}]},P=f({name:"OrderAscendingIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:o,style:c}=O(t),p=a(()=>["t-icon","t-icon-order-ascending",o.value]),u=a(()=>s(s({},c.value),r.style)),v=a(()=>({class:p.value,style:u.value,onClick:d=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:d})}}));return()=>y(m,v.value)}});export{P as default};
