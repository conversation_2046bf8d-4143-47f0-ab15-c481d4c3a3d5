import{d as y,h as a,ab as d,ac as O,ad as b}from"./index-R826otwI.js";function o(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?o(Object(r),!0).forEach(function(t){b(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var C={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M7 2V5H16.1707C16.472 4.14759 17.1476 3.47199 18 3.17071V2H20V3.17071C21.1652 3.58254 22 4.69378 22 6C22 7.30622 21.1652 8.41746 20 8.82929V19H8.82929C8.52801 19.8524 7.85241 20.528 7 20.8293V22H5V20.8293C4.14759 20.528 3.47199 19.8524 3.17071 19H2V17H3.17071C3.47199 16.1476 4.14759 15.472 5 15.1707L5 7H2V5H5V2H7ZM7 7V15.1707C7.85241 15.472 8.52801 16.1476 8.82929 17H18V8.82929C17.1476 8.52801 16.472 7.85241 16.1707 7H7Z"}}]},g=y({name:"SubwayLineFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:c}=d(t),p=a(()=>["t-icon","t-icon-subway-line-filled",l.value]),u=a(()=>s(s({},c.value),r.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:v})}}));return()=>O(C,f.value)}});export{g as default};
