import{T as f}from"./index-4XUcSWmk.js";import{d as B,q as e,f as d,b as C,o as x,n as a,g as t,u as s,p as r,t as n,a as i,y as w,_ as L}from"./index-CTFw1yDv.js";const v=[{growUp:1,productName:"国家电网有限公司",count:7059,date:"2021-09-01"},{growUp:-1,productName:"深圳燃气集团股份有限公司",count:6437,date:"2021-09-01"},{growUp:4,productName:"国家烟草专卖局",count:4221,date:"2021-09-01"},{growUp:3,productName:"中国电信集团有限公司",count:3317,date:"2021-09-01"},{growUp:-3,productName:"中国移动通信集团有限公司",count:3015,date:"2021-09-01"},{growUp:-3,productName:"新余市办公用户采购项目",count:2015,date:"2021-09-12"}],K=[{growUp:1,productName:"腾讯科技（深圳）有限公司",count:3015,date:"2021-09-01"},{growUp:-1,productName:"大润发有限公司",count:2015,date:"2021-09-01"},{growUp:6,productName:"四川海底捞股份有限公司",count:1815,date:"2021-09-11"},{growUp:-3,productName:"索尼（中国）有限公司",count:1015,date:"2021-09-21"},{growUp:-4,productName:"松下电器（中国）有限公司",count:445,date:"2021-09-19"},{growUp:-3,productName:"新余市办公用户采购项目",count:2015,date:"2021-09-12"}],V=B({__name:"RankList",setup(S){const k=[{align:"center",colKey:"index",title:e("pages.dashboardBase.saleColumns.index"),width:70,fixed:"left"},{align:"left",ellipsis:!0,colKey:"productName",title:e("pages.dashboardBase.saleColumns.productName"),width:150},{align:"center",colKey:"growUp",width:70,title:e("pages.dashboardBase.saleColumns.growUp")},{align:"center",colKey:"count",title:e("pages.dashboardBase.saleColumns.count"),width:70},{align:"center",colKey:"operation",title:e("pages.dashboardBase.saleColumns.operation"),width:70,fixed:"right"}],U=[{align:"center",colKey:"index",title:e("pages.dashboardBase.buyColumns.index"),width:70,fixed:"left"},{align:"left",ellipsis:!0,colKey:"productName",width:150,title:e("pages.dashboardBase.buyColumns.productName")},{align:"center",colKey:"growUp",width:70,title:e("pages.dashboardBase.buyColumns.growUp")},{align:"center",colKey:"count",title:e("pages.dashboardBase.buyColumns.count"),width:70},{align:"center",colKey:"operation",title:e("pages.dashboardBase.buyColumns.operation"),width:70,fixed:"right"}],p=l=>{console.log(l)},u=l=>["dashboard-rank",{"dashboard-rank__top":l<3}];return(l,T)=>{const c=d("t-radio-button"),g=d("t-radio-group"),_=d("t-link"),m=d("t-table"),h=d("t-card"),b=d("t-col"),y=d("t-row");return x(),C(y,{gutter:16,class:"row-container"},{default:a(()=>[t(b,{xs:12,xl:6},{default:a(()=>[t(h,{title:s(e)("pages.dashboardBase.rankList.title"),class:"dashboard-rank-card",bordered:!1},{actions:a(()=>[t(g,{"default-value":"dateVal",variant:"default-filled"},{default:a(()=>[t(c,{value:"dateVal"},{default:a(()=>[r(n(s(e)("pages.dashboardBase.rankList.week")),1)]),_:1}),t(c,{value:"monthVal"},{default:a(()=>[r(n(s(e)("pages.dashboardBase.rankList.month")),1)]),_:1})]),_:1})]),default:a(()=>[t(m,{data:s(v),columns:k,"row-key":"productName"},{index:a(({rowIndex:o})=>[i("span",{class:w(u(o))},n(o+1),3)]),growUp:a(({row:o})=>[i("span",null,[t(f,{type:o.growUp>0?"up":"down",describe:Math.abs(o.growUp)},null,8,["type","describe"])])]),operation:a(o=>[t(_,{theme:"primary",onClick:N=>p(o)},{default:a(()=>[r(n(s(e)("pages.dashboardBase.rankList.info")),1)]),_:2},1032,["onClick"])]),_:1},8,["data"])]),_:1},8,["title"])]),_:1}),t(b,{xs:12,xl:6},{default:a(()=>[t(h,{title:s(e)("pages.dashboardBase.rankList.title"),class:"dashboard-rank-card",bordered:!1},{actions:a(()=>[t(g,{"default-value":"dateVal",variant:"default-filled"},{default:a(()=>[t(c,{value:"dateVal"},{default:a(()=>[r(n(s(e)("pages.dashboardBase.rankList.week")),1)]),_:1}),t(c,{value:"monthVal"},{default:a(()=>[r(n(s(e)("pages.dashboardBase.rankList.month")),1)]),_:1})]),_:1})]),default:a(()=>[t(m,{data:s(K),columns:U,"row-key":"productName"},{index:a(({rowIndex:o})=>[i("span",{class:w(u(o))},n(o+1),3)]),growUp:a(({row:o})=>[t(f,{type:o.growUp>0?"up":"down",describe:Math.abs(o.growUp)},null,8,["type","describe"])]),operation:a(o=>[t(_,{theme:"primary",onClick:N=>p(o)},{default:a(()=>[r(n(s(e)("pages.dashboardBase.rankList.info")),1)]),_:2},1032,["onClick"])]),_:1},8,["data"])]),_:1},8,["title"])]),_:1})]),_:1})}}}),D=L(V,[["__scopeId","data-v-cb321cfb"]]);export{D as default};
