import{d as O,h as a,ab as m,ac as y,ad as d}from"./index-R826otwI.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){d(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var C={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M12.0036 3C8.46627 3 5.35709 4.83591 3.57792 7.61157L3.03828 8.45347L1.35449 7.37418L1.89413 6.53229C4.0255 3.20715 7.75648 1 12.0036 1C16.2507 1 19.9817 3.20715 22.1131 6.53229L22.6527 7.37418L20.9689 8.45347L20.4293 7.61157C18.6501 4.83591 15.5409 3 12.0036 3ZM11.9922 6.98828C10.2241 6.98828 8.6695 7.90531 7.77909 9.29443L7.23945 10.1363L5.55566 9.05704L6.09531 8.21515C7.33792 6.27656 9.51431 4.98828 11.9922 4.98828C14.4702 4.98828 16.6466 6.27656 17.8892 8.21515L18.4288 9.05704L16.745 10.1363L16.2054 9.29443C15.315 7.90531 13.7604 6.98828 11.9922 6.98828ZM4.9997 11H18.9997V23H16.9997V13H6.9997V23H4.9997V11ZM10.9997 15H13.0036V17.0039H10.9997V15Z"}}]},g=O({name:"RemoteWaveIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:o,style:c}=m(r),p=a(()=>["t-icon","t-icon-remote-wave",o.value]),u=a(()=>s(s({},c.value),t.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:f})}}));return()=>y(C,v.value)}});export{g as default};
