import{d as C,h as a,ab as O,ac as y,ad as d}from"./index-R826otwI.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){d(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var h={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M6.75 2.5H17.25V5.25C17.25 6.82062 16.5603 8.23016 15.4672 9.19226C17.3447 9.97683 18.8992 11.379 19.8768 13.1431L20.3615 14.0178L18.6122 14.9872L18.1275 14.1126C16.9324 11.9561 14.6356 10.5 12 10.5C8.13401 10.5 5 13.634 5 17.5V18C5 19.1046 5.89543 20 7 20H14V22H7C4.79086 22 3 20.2091 3 18V17.5C3 13.7583 5.28335 10.5499 8.53267 9.19218C7.43966 8.23008 6.75 6.82058 6.75 5.25V2.5ZM12 8.5C13.7949 8.5 15.25 7.04493 15.25 5.25V4.5H8.75V5.25C8.75 7.04493 10.2051 8.5 12 8.5ZM16 16H23V18H16V16ZM16 20H23V22H16V20Z"}}]},b=C({name:"WealthIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:l,style:c}=O(r),p=a(()=>["t-icon","t-icon-wealth",l.value]),u=a(()=>s(s({},c.value),t.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:f})}}));return()=>y(h,v.value)}});export{b as default};
