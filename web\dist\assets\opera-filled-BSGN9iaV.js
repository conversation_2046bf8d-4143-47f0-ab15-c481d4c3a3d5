import{d as v,h as a,ab as d,ac as O,ad as y}from"./index-R826otwI.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){y(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M6.49885 5H7.99885C11.6601 5 14.0647 6.46441 15.5776 8.50988C16.5869 9.87454 17.1704 11.4572 17.5106 12.9403C19.1908 11.6651 21.1814 11.1395 23.221 10.7486L21.8462 19.0113C15.3033 19.0107 8.76047 19.0107 2.21762 19.0096L0.71875 13.001H1.99885C3.02025 13.001 3.89692 13.1059 4.59466 13.2373L3.67315 10.0117H4.99885C6.29516 10.0117 7.55312 10.2432 8.50249 10.4787C8.12289 9.16244 7.65362 7.77145 7.07577 6.38462L6.49885 5ZM9.09065 12.7244C8.84984 12.6465 8.52977 12.5501 8.15388 12.4535C7.62868 12.3185 7.00783 12.1867 6.35055 12.1028L7.75309 17.0106C8.37066 17.0107 8.73442 17.0109 9.06955 17.0112C9.32511 17.0113 9.56402 17.0115 9.88613 17.0116C9.88426 16.9939 9.8824 16.9764 9.88057 16.959C9.78001 16.0094 9.73348 15.5701 9.65549 15.1277C9.57751 14.6853 9.471 14.2565 9.2408 13.3298C9.19573 13.1483 9.14592 12.9478 9.09065 12.7244ZM5.67287 17.0102L5.22568 15.4458C5.08942 15.4042 4.92194 15.3573 4.7254 15.3099C4.34919 15.2193 3.86763 15.1274 3.29558 15.0677L3.78007 17.0099L5.67287 17.0102ZM17.9814 17.0101L20.1515 17.0108L20.7481 13.4256C20.6554 13.4618 20.5603 13.5005 20.4633 13.5419C19.5691 13.9232 18.5909 14.4945 17.8856 15.3055C17.9368 15.8736 17.9644 16.4717 17.9796 16.9524C17.9802 16.9718 17.9808 16.991 17.9814 17.0101Z"}}]},g=v({name:"OperaFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:c}=d(t),p=a(()=>["t-icon","t-icon-opera-filled",l.value]),C=a(()=>s(s({},c.value),r.style)),u=a(()=>({class:p.value,style:C.value,onClick:f=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:f})}}));return()=>O(m,u.value)}});export{g as default};
