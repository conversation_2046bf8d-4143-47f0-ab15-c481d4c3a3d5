import{d as C,h as a,ab as d,ac as O,ad as y}from"./index-R826otwI.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){y(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var g={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M10.9998 15.4998C10.9998 14.4516 11.6442 13.9998 11.9998 13.9998C12.3554 13.9998 12.9998 14.4516 12.9998 15.4998C12.9998 16.548 12.3554 16.9998 11.9998 16.9998C11.6442 16.9998 10.9998 16.548 10.9998 15.4998Z"}},{tag:"path",attrs:{fill:"currentColor",d:"M12 23C5.92487 23 1 18.0751 1 12C1 5.92487 5.92487 1 12 1C18.0751 1 23 5.92487 23 12C23 18.0751 18.0751 23 12 23ZM10.2327 10.1338L6.76855 8.13379L5.76855 9.86584L9.23266 11.8658L10.2327 10.1338ZM14.7686 11.8658L18.2327 9.86584L17.2327 8.13379L13.7686 10.1338L14.7686 11.8658ZM11.9998 11.9998C10.1463 11.9998 8.99981 13.7866 8.99981 15.4998C8.99981 17.213 10.1463 18.9998 11.9998 18.9998C13.8534 18.9998 14.9998 17.213 14.9998 15.4998C14.9998 13.7866 13.8534 11.9998 11.9998 11.9998Z"}}]},b=C({name:"Swear2FilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:c}=d(t),p=a(()=>["t-icon","t-icon-swear-2-filled",l.value]),u=a(()=>s(s({},c.value),r.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>O(g,f.value)}});export{b as default};
