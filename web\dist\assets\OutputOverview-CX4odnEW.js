import{d as W,ag as D,r as N,h as z,i as $,Y as I,ah as V,K as p,ai as A,f as i,b as F,o as L,n as e,g as t,y as g,a as o,u as Y,p as m,t as _,_ as H}from"./index-R826otwI.js";import{T as w}from"./index-CTUDyomP.js";import{L as C}from"./date-DdgLqdlw.js";import{c as k}from"./index-JBp7_miI.js";import{u as K,i as M,a as j,b as q}from"./installCanvasRenderer-akfsAsjg.js";import{i as G,a as J,b as P}from"./install-DYqkEeHz.js";import"./dayjs.min-Dvo2iNbV.js";import"./charts-PDyqFShm.js";const Q={class:"inner-card__content"},R={class:"inner-card__content-footer"},U={class:"inner-card__content"},X={class:"inner-card__content-footer"},Z={name:"DashboardBase"},ee=W({...Z,setup(te){K([G,J,P,M,j]);const c=D(),u=N(1),h=z(()=>c.chartColors);let r,n;const x=()=>{r||(r=document.getElementById("stokeContainer")),n=q(r),n.setOption(k({dateTime:C,...h.value}))},f=()=>{x()},v=()=>{document.documentElement.clientWidth>=1400&&document.documentElement.clientWidth<1920?u.value=Number((document.documentElement.clientWidth/2080).toFixed(2)):document.documentElement.clientWidth<1080?u.value=Number((document.documentElement.clientWidth/1080).toFixed(2)):u.value=1,n.resize({width:r.clientWidth,height:r.clientHeight})};$(()=>{f(),I(()=>{v()})});const{width:B,height:O}=V();p([B,O],()=>{v()}),p(()=>c.brandTheme,()=>{A([n])}),p(()=>c.mode,()=>{[n].forEach(a=>{a.dispose()}),f()});const T=a=>{n.setOption(k({dateTime:a,...h.value}))};return(a,s)=>{const S=i("t-date-range-picker"),d=i("t-card"),l=i("t-col"),y=i("t-button"),b=i("t-row");return L(),F(d,{bordered:!1},{default:e(()=>[t(b,null,{default:e(()=>[t(l,{xs:12,xl:9},{default:e(()=>[t(d,{bordered:!1,title:a.t("pages.dashboardBase.outputOverview.title"),subtitle:a.t("pages.dashboardBase.outputOverview.subtitle"),class:g({"dashboard-overview-card":!0,"overview-panel":!0})},{actions:e(()=>[t(S,{class:"card-date-picker-container",theme:"primary",mode:"date","default-value":Y(C),onChange:s[0]||(s[0]=E=>T(E))},null,8,["default-value"])]),default:e(()=>[s[1]||(s[1]=o("div",{id:"stokeContainer",style:{width:"100%",height:"351px"},class:"dashboard-chart-container"},null,-1))]),_:1,__:[1]},8,["title","subtitle"])]),_:1}),t(l,{xs:12,xl:3},{default:e(()=>[t(d,{bordered:!1,class:g({"dashboard-overview-card":!0,"export-panel":!0})},{actions:e(()=>[t(y,null,{default:e(()=>[m(_(a.t("pages.dashboardBase.outputOverview.export")),1)]),_:1})]),default:e(()=>[t(b,null,{default:e(()=>[t(l,{xs:6,xl:12},{default:e(()=>[t(d,{bordered:!1,subtitle:a.t("pages.dashboardBase.outputOverview.month.input"),class:"inner-card"},{default:e(()=>[o("div",Q,[s[2]||(s[2]=o("div",{class:"inner-card__content-title"},"1726",-1)),o("div",R,[m(_(a.t("pages.dashboardBase.outputOverview.since"))+" ",1),t(w,{class:"trend-tag",type:"down","is-reverse-color":!1,describe:"20.3%"})])])]),_:1},8,["subtitle"])]),_:1}),t(l,{xs:6,xl:12},{default:e(()=>[t(d,{bordered:!1,subtitle:a.t("pages.dashboardBase.outputOverview.month.output"),class:"inner-card"},{default:e(()=>[o("div",U,[s[3]||(s[3]=o("div",{class:"inner-card__content-title"},"226",-1)),o("div",X,[m(_(a.t("pages.dashboardBase.outputOverview.since"))+" ",1),t(w,{class:"trend-tag",type:"down","is-reverse-color":!1,describe:"20.3%"})])])]),_:1},8,["subtitle"])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})}}}),ce=H(ee,[["__scopeId","data-v-9144ccc3"]]);export{ce as default};
