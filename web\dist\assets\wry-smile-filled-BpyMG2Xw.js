import{d as y,h as a,ab as d,ac as O,ad as m}from"./index-R826otwI.js";function o(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?o(Object(r),!0).forEach(function(t){m(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var C={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M12 23C5.92487 23 1 18.0751 1 12C1 5.92487 5.92487 1 12 1C18.0751 1 23 5.92487 23 12C23 18.0751 18.0751 23 12 23ZM6.76855 11.8658L10.2327 9.86584L9.23266 8.13379L5.76855 10.1338L6.76855 11.8658ZM18.2327 10.1338L14.7686 8.13379L13.7686 9.86584L17.2327 11.8658L18.2327 10.1338ZM9.40058 14.4991L8.89985 13.6335L7.16864 14.6349L7.66936 15.5005C8.53238 16.9924 10.1479 17.9998 11.9998 17.9998C13.8518 17.9998 15.4673 16.9924 16.3303 15.5005L16.831 14.6349L15.0998 13.6335L14.5991 14.4991C14.0789 15.3984 13.109 15.9998 11.9998 15.9998C10.8907 15.9998 9.9208 15.3984 9.40058 14.4991Z"}}]},b=y({name:"WrySmileFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:c}=d(t),p=a(()=>["t-icon","t-icon-wry-smile-filled",l.value]),u=a(()=>s(s({},c.value),r.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:v})}}));return()=>O(C,f.value)}});export{b as default};
