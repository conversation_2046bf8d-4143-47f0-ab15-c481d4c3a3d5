using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using GCP.Common;
using GCP.Eventbus.Infrastructure;
using Serilog;

namespace GCP.Eventbus.Providers
{
    /// <summary>
    /// TCP消息响应示例
    /// 展示如何根据执行的函数结果，回复对应客户端
    /// </summary>
    public class TcpResponseExample
    {
        private TcpMessageBus _messageBus;

        public TcpResponseExample(TcpMessageBus messageBus)
        {
            _messageBus = messageBus;
        }

        /// <summary>
        /// 设置消息处理器
        /// </summary>
        public async Task SetupMessageHandlers()
        {
            // 订阅用户查询请求
            await _messageBus.SubscribeAsync("user.query", HandleUserQuery, new ConsumerOptions
            {
                Name = "UserQueryHandler"
            });

            // 订阅数据处理请求
            await _messageBus.SubscribeAsync("data.process", HandleDataProcess, new ConsumerOptions
            {
                Name = "DataProcessHandler"
            });

            // 订阅函数执行请求
            await _messageBus.SubscribeAsync("function.execute", HandleFunctionExecute, new ConsumerOptions
            {
                Name = "FunctionExecuteHandler"
            });
        }

        /// <summary>
        /// 处理用户查询请求
        /// </summary>
        private async Task HandleUserQuery(MessageEnvelope message, CancellationToken cancellationToken)
        {
            try
            {
                Log.Information("收到用户查询请求，客户端ID: {ClientId}", message.GetClientId());

                // 解析请求参数
                var queryRequest = JsonHelper.Deserialize<UserQueryRequest>(message.Payload.ToString());
                
                // 执行查询逻辑
                var userData = await QueryUserData(queryRequest.UserId);

                // 创建成功响应
                var response = TcpMessageResponseHelper.CreateSuccessResponse(userData, "查询成功");

                // 回复给发送方
                await _messageBus.ReplyToSenderAsync(message, "user.query.response", response, 
                    new Dictionary<string, object> { ["RequestId"] = queryRequest.RequestId }, cancellationToken);

                Log.Information("用户查询处理完成，已回复客户端 {ClientId}", message.GetClientId());
            }
            catch (Exception ex)
            {
                Log.Error(ex, "处理用户查询时发生错误");

                // 创建错误响应
                var errorResponse = TcpMessageResponseHelper.CreateErrorResponse($"查询失败: {ex.Message}", "QUERY_ERROR");

                // 回复错误给发送方
                await _messageBus.ReplyToSenderAsync(message, "user.query.response", errorResponse, cancellationToken: cancellationToken);
            }
        }

        /// <summary>
        /// 处理数据处理请求
        /// </summary>
        private async Task HandleDataProcess(MessageEnvelope message, CancellationToken cancellationToken)
        {
            try
            {
                Log.Information("收到数据处理请求，客户端ID: {ClientId}", message.GetClientId());

                var processRequest = JsonHelper.Deserialize<DataProcessRequest>(message.Payload.ToString());

                // 执行数据处理
                var result = await ProcessData(processRequest.Data, processRequest.ProcessType);

                // 回复处理结果
                var response = TcpMessageResponseHelper.CreateSuccessResponse(result, "数据处理完成");
                await _messageBus.ReplyToSenderAsync(message, "data.process.response", response, cancellationToken: cancellationToken);

                Log.Information("数据处理完成，已回复客户端 {ClientId}", message.GetClientId());
            }
            catch (Exception ex)
            {
                Log.Error(ex, "处理数据时发生错误");
                var errorResponse = TcpMessageResponseHelper.CreateErrorResponse($"数据处理失败: {ex.Message}", "PROCESS_ERROR");
                await _messageBus.ReplyToSenderAsync(message, "data.process.response", errorResponse, cancellationToken: cancellationToken);
            }
        }

        /// <summary>
        /// 处理函数执行请求
        /// </summary>
        private async Task HandleFunctionExecute(MessageEnvelope message, CancellationToken cancellationToken)
        {
            try
            {
                Log.Information("收到函数执行请求，客户端ID: {ClientId}", message.GetClientId());

                var executeRequest = JsonHelper.Deserialize<FunctionExecuteRequest>(message.Payload.ToString());

                // 执行函数
                var result = await ExecuteFunction(executeRequest.FunctionName, executeRequest.Parameters);

                // 回复执行结果
                var response = TcpMessageResponseHelper.CreateSuccessResponse(result, "函数执行成功");
                await _messageBus.ReplyToSenderAsync(message, "function.execute.response", response, 
                    new Dictionary<string, object> 
                    { 
                        ["RequestId"] = executeRequest.RequestId,
                        ["FunctionName"] = executeRequest.FunctionName
                    }, cancellationToken);

                Log.Information("函数 {FunctionName} 执行完成，已回复客户端 {ClientId}", 
                    executeRequest.FunctionName, message.GetClientId());
            }
            catch (Exception ex)
            {
                Log.Error(ex, "执行函数时发生错误");
                var errorResponse = TcpMessageResponseHelper.CreateErrorResponse($"函数执行失败: {ex.Message}", "FUNCTION_ERROR");
                await _messageBus.ReplyToSenderAsync(message, "function.execute.response", errorResponse, cancellationToken: cancellationToken);
            }
        }

        /// <summary>
        /// 模拟用户数据查询
        /// </summary>
        private async Task<object> QueryUserData(string userId)
        {
            await Task.Delay(100); // 模拟数据库查询
            return new
            {
                UserId = userId,
                UserName = $"User_{userId}",
                Email = $"user_{userId}@example.com",
                CreateTime = DateTime.Now
            };
        }

        /// <summary>
        /// 模拟数据处理
        /// </summary>
        private async Task<object> ProcessData(object data, string processType)
        {
            await Task.Delay(200); // 模拟数据处理
            return new
            {
                ProcessedData = data,
                ProcessType = processType,
                ProcessTime = DateTime.Now,
                Status = "Completed"
            };
        }

        /// <summary>
        /// 模拟函数执行
        /// </summary>
        private async Task<object> ExecuteFunction(string functionName, Dictionary<string, object> parameters)
        {
            await Task.Delay(150); // 模拟函数执行
            return new
            {
                FunctionName = functionName,
                Parameters = parameters,
                Result = $"Function {functionName} executed successfully",
                ExecuteTime = DateTime.Now
            };
        }
    }

    // 请求数据模型
    public class UserQueryRequest
    {
        public string RequestId { get; set; }
        public string UserId { get; set; }
    }

    public class DataProcessRequest
    {
        public string RequestId { get; set; }
        public object Data { get; set; }
        public string ProcessType { get; set; }
    }

    public class FunctionExecuteRequest
    {
        public string RequestId { get; set; }
        public string FunctionName { get; set; }
        public Dictionary<string, object> Parameters { get; set; }
    }
}
