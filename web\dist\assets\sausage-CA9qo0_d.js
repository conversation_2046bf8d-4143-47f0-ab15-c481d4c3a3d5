import{d as O,h as a,ab as y,ac as d,ad as g}from"./index-R826otwI.js";function l(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function i(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?l(Object(r),!0).forEach(function(t){g(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var C={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M19.9983 2.00293V3.99941H22.0023V5.99941H20.7451C21.2068 7.45561 20.8602 9.11256 19.7054 10.2674L10.2656 19.7072C9.11077 20.862 7.45381 21.2086 5.99761 20.7469L5.99761 22.001H3.99761L3.99761 20.0001H2.00195V18.0001H3.2508C2.78915 16.5439 3.13571 14.887 4.2905 13.7322L13.7304 4.2923C14.8852 3.13751 16.5421 2.79095 17.9983 3.2526V2.00293H19.9983ZM18.2912 5.70652C17.4223 4.8376 16.0135 4.8376 15.1446 5.70652L5.70472 15.1464C4.8358 16.0153 4.8358 17.4241 5.70472 18.293C6.57363 19.1619 7.98243 19.1619 8.85134 18.293L18.2912 8.85314C19.1601 7.98422 19.1601 6.57543 18.2912 5.70652Z"}}]},b=O({name:"SausageIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:o,style:c}=y(t),p=a(()=>["t-icon","t-icon-sausage",o.value]),u=a(()=>i(i({},c.value),r.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var s;return(s=e.onClick)===null||s===void 0?void 0:s.call(e,{e:f})}}));return()=>d(C,v.value)}});export{b as default};
