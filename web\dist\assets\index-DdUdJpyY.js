import{R as l}from"./index-CqfrbUmy.js";import{d as u,f as p,b as i,o as d,n as r,a as f,g as s,p as a,t as n}from"./index-CTFw1yDv.js";const k={name:"ResultNetworkError"},_=u({...k,setup(m){return(t,e)=>{const o=p("t-button");return d(),i(l,{title:t.t("pages.result.networkError.title"),tip:t.t("pages.result.networkError.subtitle"),type:"wifi"},{default:r(()=>[f("div",null,[s(o,{theme:"default",onClick:e[0]||(e[0]=()=>t.$router.push("/"))},{default:r(()=>[a(n(t.t("pages.result.networkError.back")),1)]),_:1}),s(o,{onClick:e[1]||(e[1]=()=>t.$router.push("/"))},{default:r(()=>[a(n(t.t("pages.result.networkError.reload")),1)]),_:1})])]),_:1},8,["title","tip"])}}});export{_ as default};
