import{d as C,h as a,ab as d,ac as O,ad as y}from"./index-CTFw1yDv.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){y(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M20.765 2.36781L20.4488 3.31649C20.0895 4.39438 19.4791 5.43795 18.9281 6.22198C19.9961 7.44469 20.8823 9.09613 21.2715 10.8132C21.7399 12.8796 21.5151 15.2132 19.7783 16.95L17.6569 19.0713L16.9308 18.3452C15.033 20.0489 12.836 21.3089 10.667 21.8332C8.19914 22.4297 5.66908 22.0835 3.79301 20.2074C1.91693 18.3313 1.57066 15.8013 2.16718 13.3334C2.69146 11.1643 3.95146 8.96739 5.65513 7.06953L4.92902 6.34342L7.05034 4.2221C8.77692 2.49551 11.1094 2.272 13.1607 2.72285C14.7607 3.07452 16.3031 3.85434 17.474 4.80506C17.8869 4.18205 18.3044 3.42512 18.5514 2.68403L18.8677 1.73535L20.765 2.36781ZM15.5144 16.9288L14.5356 15.95V13.7074H12.293L7.0716 8.486C5.58182 10.1675 4.53594 12.046 4.1112 13.8032C3.62787 15.8029 3.9591 17.5451 5.20722 18.7932C6.45534 20.0413 8.19752 20.3725 10.1971 19.8892C11.9544 19.4644 13.8329 18.4185 15.5144 16.9288Z"}}]},g=C({name:"NutFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:l,style:c}=d(r),p=a(()=>["t-icon","t-icon-nut-filled",l.value]),u=a(()=>s(s({},c.value),t.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>O(m,f.value)}});export{g as default};
