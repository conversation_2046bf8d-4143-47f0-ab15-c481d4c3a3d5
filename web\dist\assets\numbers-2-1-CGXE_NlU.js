import{d as O,h as a,ab as m,ac as y,ad as b}from"./index-CTFw1yDv.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){b(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var d={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M7 8C7 5.23858 9.23858 3 12 3H13V3.10002C15.2822 3.56329 17 5.58104 17 8V8.06512C17 9.59037 16.3127 11.0344 15.1289 11.9962L9 16.976V18H17V20H7V16.976C7 16.3737 7.27139 15.8035 7.73881 15.4237L13.8677 10.444C14.5841 9.86197 15 8.98812 15 8.06512V8C15 6.34315 13.6569 5 12 5C10.3431 5 9 6.34315 9 8V9H7V8Z"}}]},g=O({name:"Numbers21Icon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:o,style:c}=m(t),p=a(()=>["t-icon","t-icon-numbers-2-1",o.value]),u=a(()=>s(s({},c.value),r.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:f})}}));return()=>y(d,v.value)}});export{g as default};
