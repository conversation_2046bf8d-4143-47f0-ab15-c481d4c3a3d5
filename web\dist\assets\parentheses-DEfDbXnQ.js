import{d as O,h as a,ab as y,ac as d,ad as C}from"./index-CTFw1yDv.js";function l(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function i(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?l(Object(r),!0).forEach(function(t){C(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var h={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M5.44539 4.53903L5.00736 5.43799C4.04211 7.41891 3.5 9.64472 3.5 12C3.5 14.3552 4.04211 16.581 5.00736 18.5619L5.44539 19.4609L3.64748 20.337L3.20944 19.438C2.11414 17.1902 1.5 14.6654 1.5 12C1.5 9.33456 2.11414 6.80976 3.20944 4.56192L3.64748 3.66296L5.44539 4.53903ZM20.3525 3.66296L20.7906 4.56192C21.8859 6.80976 22.5 9.33456 22.5 12C22.5 14.6654 21.8859 17.1902 20.7906 19.438L20.3525 20.337L18.5546 19.4609L18.9926 18.5619C19.9579 16.581 20.5 14.3552 20.5 12C20.5 9.64472 19.9579 7.41891 18.9926 5.43799L18.5546 4.53903L20.3525 3.66296Z"}}]},b=O({name:"ParenthesesIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:o,style:c}=y(t),p=a(()=>["t-icon","t-icon-parentheses",o.value]),u=a(()=>i(i({},c.value),r.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var s;return(s=e.onClick)===null||s===void 0?void 0:s.call(e,{e:f})}}));return()=>d(h,v.value)}});export{b as default};
