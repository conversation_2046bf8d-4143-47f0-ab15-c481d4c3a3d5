import{d,h as a,ab as O,ac as g,ad as y}from"./index-R826otwI.js";function l(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?l(Object(r),!0).forEach(function(t){y(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M10.465 3.25967L10.915 1.58545L9.24046 2.03415C3.9058 3.46357 0.739977 8.94694 2.1694 14.2816C2.21958 14.4689 2.33295 14.7676 2.4099 14.9662C2.45296 15.0772 2.49364 15.1797 2.52348 15.2542L2.82731 16.0001H8.7L11.9993 13.8L15.3 16.0001H20.6173L20.8937 15.4473C21.4381 14.3586 21.7502 13.3323 21.8128 12.2988L21.9163 10.5877L20.3758 11.3398C19.9773 11.5344 19.5538 11.694 19.1078 11.8135C15.3735 12.8141 11.5352 10.598 10.5346 6.86378C10.1951 5.59695 10.165 4.37554 10.465 3.25967Z"}},{tag:"path",attrs:{fill:"currentColor",d:"M9.30278 20.0002H1V18.0002H8.69722L12 15.7983L15.3028 18.0002H23V20.0002H14.6972L12 18.202L9.30278 20.0002Z"}}]},b=d({name:"MoonRisingFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:o,style:c}=O(t),p=a(()=>["t-icon","t-icon-moon-rising-filled",o.value]),u=a(()=>s(s({},c.value),r.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:v})}}));return()=>g(m,f.value)}});export{b as default};
