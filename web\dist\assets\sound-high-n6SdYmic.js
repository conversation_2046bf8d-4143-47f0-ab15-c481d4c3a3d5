import{d as f,h as a,ab as d,ac as O,ad as y}from"./index-R826otwI.js";function l(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?l(Object(r),!0).forEach(function(t){y(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var g={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M15 1.29004V22.7095L5.7369 17.4998L1 17.4998L1 6.49976H5.7369L15 1.29004ZM4.99882 8.49976H3L3 15.4998L4.99882 15.4998L4.99882 8.49976ZM6.99882 15.9149L13 19.29V4.70949L6.99882 8.08465V15.9149ZM20.4497 5.6358L21.1569 6.34291C24.281 9.4671 24.281 14.5324 21.1569 17.6566L20.4497 18.3637L19.0355 16.9495L19.7426 16.2424C22.0858 13.8993 22.0858 10.1003 19.7426 7.75712L19.0355 7.05002L20.4497 5.6358ZM17.2678 8.81778L18.682 7.40357L19.3891 8.11068C21.537 10.2586 21.537 13.741 19.3891 15.8888L18.682 16.596L17.2678 15.1817L17.9749 14.4746C19.3417 13.1078 19.3417 10.8917 17.9749 9.52489L17.2678 8.81778ZM16.9142 9.17134L17.6213 9.87844C18.7929 11.05 18.7929 12.9495 17.6213 14.1211L16.9142 14.8282L15.5 13.414L16.2071 12.7069C16.5976 12.3163 16.5976 11.6832 16.2071 11.2927L15.5 10.5855L16.9142 9.17134Z"}}]},m=f({name:"SoundHighIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:o,style:c}=d(t),p=a(()=>["t-icon","t-icon-sound-high",o.value]),u=a(()=>s(s({},c.value),r.style)),L=a(()=>({class:p.value,style:u.value,onClick:v=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:v})}}));return()=>O(g,L.value)}});export{m as default};
