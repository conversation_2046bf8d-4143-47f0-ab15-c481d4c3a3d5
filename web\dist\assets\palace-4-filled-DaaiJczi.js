import{d,h as a,ab as O,ac as y,ad as g}from"./index-R826otwI.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function c(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){g(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M6 1.69824L10 5.03158V8.99995H14V5.03158L18 1.69824L22 5.03158V22H15.5V18.5C15.5 16.567 13.933 15 12 15C10.067 15 8.5 16.567 8.5 18.5V22H2V5.03158L6 1.69824ZM20 8.99995V5.96833L18 4.30166L16 5.96833V8.99995H20ZM4 8.99995H8V5.96833L6 4.30166L4 5.96833V8.99995Z"}},{tag:"path",attrs:{fill:"currentColor",d:"M12 17C11.1716 17 10.5 17.6716 10.5 18.5V22H13.5V18.5C13.5 17.6716 12.8284 17 12 17Z"}}]},C=d({name:"Palace4FilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:l,style:s}=O(r),p=a(()=>["t-icon","t-icon-palace-4-filled",l.value]),u=a(()=>c(c({},s.value),t.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>y(m,f.value)}});export{C as default};
