import{d as f,h as a,ab as O,ac as d,ad as g}from"./index-CTFw1yDv.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function p(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){g(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var L={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M2 3L17 3V11L2 11L2 3ZM4 5L4 9L15 9L15 5L4 5ZM2 14L22 14L22 16L2 16L2 14ZM2 19L22 19V21L2 21L2 19Z"}}]},m=f({name:"TypographyIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:o,style:s}=O(t),c=a(()=>["t-icon","t-icon-typography",o.value]),u=a(()=>p(p({},s.value),r.style)),v=a(()=>({class:c.value,style:u.value,onClick:y=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:y})}}));return()=>d(L,v.value)}});export{m as default};
