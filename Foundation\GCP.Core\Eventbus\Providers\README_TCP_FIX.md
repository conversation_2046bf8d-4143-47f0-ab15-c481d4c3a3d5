# TCP消息分包问题解决方案

## 问题描述

在使用TCP协议传输JSON消息时，经常会遇到以下问题：
1. **分包问题**：一个完整的JSON消息被分成多个TCP包发送
2. **粘包问题**：多个JSON消息被合并在一个TCP包中发送
3. **消息截断**：接收到的JSON字符串不完整，导致解析失败

## 根本原因

TCP是流式协议，不保证数据包的边界。应用层需要自己处理消息的完整性识别。

## 解决方案

### 1. 添加JSON消息适配器

我们实现了 `JsonMessageAdapter` 类，它能够：
- 基于JSON的大括号匹配识别完整的JSON消息
- 处理字符串中的转义字符和引号
- 自动缓存不完整的消息片段
- 支持粘包情况下的多消息分离

### 2. 核心算法

```csharp
// 核心逻辑：通过大括号计数识别完整JSON
foreach (char c in receivedText)
{
    if (!_inString)
    {
        if (c == '{') _braceCount++;
        else if (c == '}') 
        {
            _braceCount--;
            if (_braceCount == 0) 
            {
                // 找到完整JSON消息
                ProcessCompleteMessage();
            }
        }
    }
}
```

### 3. 使用方法

TCP消息总线现在会自动使用JSON适配器：

```csharp
// 在TcpMessageBus构造函数中自动配置
var config = new TouchSocketConfig()
    .SetListenIPHosts(port)
    .SetTcpDataHandlingAdapter(() => new JsonMessageAdapter());
```

### 4. 日志改进

增加了详细的日志记录：
- 区分适配器处理的完整消息和直接获取的可能不完整消息
- 记录JSON解析失败的详细信息
- 添加调试级别的消息跟踪

## 测试验证

可以使用 `TcpMessageBusTest.TestJsonMessageAdapter()` 方法测试以下场景：
1. 完整JSON消息
2. 分包JSON消息
3. 粘包JSON消息
4. 包含特殊字符的JSON消息

## 故障排查

### 如果仍然出现消息截断：

1. **检查发送方**：
   ```csharp
   // 发送前验证JSON完整性
   var json = JsonHelper.Serialize(message);
   if (!IsValidJson(json)) 
   {
       Log.Error("发送的JSON格式不正确: {Json}", json);
       return;
   }
   ```

2. **检查网络层**：
   - 网络连接是否稳定
   - 是否有中间代理截断数据
   - MTU设置是否合适

3. **检查编码问题**：
   ```csharp
   // 确保使用UTF-8编码
   var bytes = Encoding.UTF8.GetBytes(jsonString);
   ```

### 日志分析

- 看到 "通过适配器接收到完整JSON消息" = 适配器工作正常
- 看到 "直接从ByteBlock获取消息，可能存在分包问题" = 适配器未生效
- 看到 "无法解析TCP消息" = JSON格式问题或发送方问题

## 性能考虑

1. **内存使用**：适配器会缓存不完整的消息片段
2. **CPU开销**：需要逐字符解析来识别JSON边界
3. **建议**：对于高频消息，考虑使用固定长度头部的协议

## 替代方案

如果JSON解析方式不适合，可以考虑：
1. **固定长度头部**：消息前4字节表示消息长度
2. **分隔符方式**：使用特殊字符（如\n）分隔消息
3. **自定义协议**：设计专门的消息格式

## 配置选项

可以通过MessageBusOptions配置：
```json
{
  "Settings": {
    "Port": "7789",
    "EnableJsonAdapter": "true",
    "MaxMessageSize": "1048576"
  }
}
```
