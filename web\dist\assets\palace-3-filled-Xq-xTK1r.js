import{d,h as a,ab as C,ac as O,ad as y}from"./index-R826otwI.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function c(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){y(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var g={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M13.0018 3.08326L13.0039 2.00195L11.0039 1.99805L11.0018 3.08266C8.1632 3.558 6 6.02638 6 9H2V22H7.5V19.4999C7.5 17.5577 8.4697 16.0975 9.27012 15.217C9.68105 14.765 10.0876 14.4199 10.3925 14.1861C10.5464 14.0681 10.6785 13.9754 10.7777 13.9088L10.971 13.7849L12 13.1675L13.029 13.7849L13.2223 13.9088C13.3215 13.9754 13.4536 14.0681 13.6075 14.1861C13.9124 14.4199 14.319 14.765 14.7299 15.217C15.5303 16.0975 16.5 17.5577 16.5 19.4999V22H22V9H18C18 6.02763 15.8386 3.56007 13.0018 3.08326ZM13.002 6.99805V9.00195H10.998V6.99805H13.002Z"}},{tag:"path",attrs:{fill:"currentColor",d:"M9.5 19.5C9.5 17 12 15.5 12 15.5C12 15.5 14.5 17 14.5 19.5V22H9.5V19.5Z"}}]},b=d({name:"Palace3FilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:l,style:s}=C(r),p=a(()=>["t-icon","t-icon-palace-3-filled",l.value]),u=a(()=>c(c({},s.value),t.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>O(g,f.value)}});export{b as default};
