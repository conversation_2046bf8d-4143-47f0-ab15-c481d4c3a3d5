# TCP消息响应指南

## 概述

本指南介绍如何在TCP消息总线中根据执行的函数结果，回复对应的客户端。

## 核心功能

### 1. 自动客户端信息注入

当客户端发送消息时，系统会自动在消息头中注入客户端信息：

```csharp
// 系统自动添加
envelope.Headers["ClientId"] = client.Id;
envelope.Headers["Client"] = client; // 客户端对象引用
```

### 2. 向特定客户端发送消息

#### 方法一：通过客户端ID发送

```csharp
await messageBus.SendToClientAsync(clientId, "response.topic", responseData, headers);
```

#### 方法二：通过客户端对象发送

```csharp
var client = message.GetClient();
await messageBus.SendToClientAsync(client, "response.topic", responseData, headers);
```

#### 方法三：直接回复发送方（推荐）

```csharp
await messageBus.ReplyToSenderAsync(originalMessage, "response.topic", responseData, headers);
```

### 3. 响应数据格式化

#### 成功响应

```csharp
var successResponse = TcpMessageResponseHelper.CreateSuccessResponse(data, "操作成功");
// 生成格式：
// {
//   "Success": true,
//   "Message": "操作成功",
//   "Data": data,
//   "Timestamp": "2024-01-01T00:00:00Z"
// }
```

#### 错误响应

```csharp
var errorResponse = TcpMessageResponseHelper.CreateErrorResponse("错误信息", "ERROR_CODE");
// 生成格式：
// {
//   "Success": false,
//   "Message": "错误信息",
//   "ErrorCode": "ERROR_CODE",
//   "Timestamp": "2024-01-01T00:00:00Z"
// }
```

## 使用示例

### 1. 基本消息处理和响应

```csharp
public async Task HandleUserQuery(MessageEnvelope message, CancellationToken cancellationToken)
{
    try
    {
        // 解析请求
        var request = JsonHelper.Deserialize<UserQueryRequest>(message.Payload.ToString());
        
        // 执行业务逻辑
        var userData = await QueryUserData(request.UserId);
        
        // 创建成功响应
        var response = TcpMessageResponseHelper.CreateSuccessResponse(userData, "查询成功");
        
        // 回复给发送方
        await messageBus.ReplyToSenderAsync(message, "user.query.response", response, 
            new Dictionary<string, object> { ["RequestId"] = request.RequestId }, cancellationToken);
    }
    catch (Exception ex)
    {
        // 创建错误响应
        var errorResponse = TcpMessageResponseHelper.CreateErrorResponse($"查询失败: {ex.Message}");
        
        // 回复错误给发送方
        await messageBus.ReplyToSenderAsync(message, "user.query.response", errorResponse, cancellationToken: cancellationToken);
    }
}
```

### 2. 函数执行结果响应

```csharp
public async Task HandleFunctionExecute(MessageEnvelope message, CancellationToken cancellationToken)
{
    var executeRequest = JsonHelper.Deserialize<FunctionExecuteRequest>(message.Payload.ToString());
    
    try
    {
        // 执行函数
        var result = await ExecuteFunction(executeRequest.FunctionName, executeRequest.Parameters);
        
        // 回复执行结果
        var response = TcpMessageResponseHelper.CreateSuccessResponse(result, "函数执行成功");
        await messageBus.ReplyToSenderAsync(message, "function.execute.response", response, 
            new Dictionary<string, object> 
            { 
                ["RequestId"] = executeRequest.RequestId,
                ["FunctionName"] = executeRequest.FunctionName
            }, cancellationToken);
    }
    catch (Exception ex)
    {
        var errorResponse = TcpMessageResponseHelper.CreateErrorResponse($"函数执行失败: {ex.Message}", "FUNCTION_ERROR");
        await messageBus.ReplyToSenderAsync(message, "function.execute.response", errorResponse, cancellationToken: cancellationToken);
    }
}
```

### 3. 批量处理响应

```csharp
public async Task HandleBatchProcess(List<MessageEnvelope> messages, CancellationToken cancellationToken)
{
    foreach (var message in messages)
    {
        try
        {
            // 处理单个消息
            var result = await ProcessSingleMessage(message);
            
            // 回复单个结果
            var response = TcpMessageResponseHelper.CreateSuccessResponse(result);
            await messageBus.ReplyToSenderAsync(message, "batch.process.response", response, cancellationToken: cancellationToken);
        }
        catch (Exception ex)
        {
            // 回复单个错误
            var errorResponse = TcpMessageResponseHelper.CreateErrorResponse(ex.Message);
            await messageBus.ReplyToSenderAsync(message, "batch.process.response", errorResponse, cancellationToken: cancellationToken);
        }
    }
}
```

## 最佳实践

### 1. 错误处理

- 总是使用 try-catch 包装业务逻辑
- 向客户端发送有意义的错误信息
- 使用错误代码便于客户端处理

### 2. 响应格式

- 使用统一的响应格式（Success/Error）
- 包含时间戳便于调试
- 在响应头中包含请求ID用于追踪

### 3. 性能考虑

- 对于长时间运行的操作，考虑发送进度更新
- 使用异步方法避免阻塞
- 合理设置超时时间

### 4. 日志记录

```csharp
Log.Information("收到请求，客户端ID: {ClientId}, 请求类型: {RequestType}", 
    message.GetClientId(), requestType);

Log.Information("处理完成，已回复客户端 {ClientId}, 耗时: {ElapsedMs}ms", 
    message.GetClientId(), stopwatch.ElapsedMilliseconds);
```

## 客户端示例

客户端发送请求：

```json
{
  "messageId": "123456",
  "payload": {
    "requestId": "req_001",
    "userId": "user123"
  },
  "headers": {
    "Topic": "user.query"
  }
}
```

服务端响应：

```json
{
  "messageId": "789012",
  "payload": {
    "Success": true,
    "Message": "查询成功",
    "Data": {
      "UserId": "user123",
      "UserName": "张三",
      "Email": "<EMAIL>"
    },
    "Timestamp": "2024-01-01T12:00:00Z"
  },
  "headers": {
    "Topic": "user.query.response",
    "RequestId": "req_001"
  }
}
```

## 故障排查

1. **客户端收不到响应**
   - 检查客户端是否订阅了响应主题
   - 确认客户端连接状态
   - 查看服务端日志确认是否发送成功

2. **响应发送失败**
   - 检查客户端是否已断开连接
   - 确认消息格式是否正确
   - 查看网络连接状态

3. **性能问题**
   - 检查是否有阻塞操作
   - 考虑使用批量处理
   - 优化数据序列化
