import{d as O,h as a,ab as y,ac as d,ad as C}from"./index-CTFw1yDv.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function c(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){C(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M13.0039 2.00195L13.0018 3.08326C15.8386 3.56007 18 6.02763 18 9H22V22H2V9H6C6 6.02638 8.1632 3.558 11.0018 3.08266L11.0039 1.99805L13.0039 2.00195ZM12 5C9.79086 5 8 6.79086 8 9V11H4V20H8V18.3541C8 15.9197 9.3754 13.6943 11.5528 12.6056L12 12.382L12.4472 12.6056C14.6246 13.6943 16 15.9197 16 18.3541V20H20V11H16V9C16 6.79086 14.2091 5 12 5ZM14 20V18.3541C14 16.8507 13.2384 15.463 12 14.6515C10.7616 15.463 10 16.8507 10 18.3541V20H14ZM10.998 7.99804H13.002V10.002H10.998V7.99804Z"}}]},g=O({name:"Palace3Icon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:s}=y(t),p=a(()=>["t-icon","t-icon-palace-3",l.value]),u=a(()=>c(c({},s.value),r.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:f})}}));return()=>d(m,v.value)}});export{g as default};
