import{d,h as a,ab as O,ac as y,ad as g}from"./index-R826otwI.js";function o(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?o(Object(t),!0).forEach(function(r){g(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):o(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var h={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M11.1 3C11.0696 3 11.0392 3.00021 11.0088 3.00062 7.55127 3.04754 4.7 5.77081 4.7 9.2 4.7 9.28787 4.70189 9.37536 4.70565 9.46243 3.12452 10.158 2 11.7035 2 13.5333 2 15.309 3.06313 16.8214 4.56833 17.5417L4.77305 17.6397H18.5012L18.6872 17.5614C20.6183 16.7492 22 14.8773 22 12.6667 22 10.9439 21.1579 9.42182 19.8726 8.45334 19.6692 8.30005 19.4545 8.16039 19.2299 8.03569 18.6217 7.69802 17.9415 7.47026 17.2193 7.37863 16.5652 5.31155 14.8331 3.7225 12.6858 3.192 12.1775 3.06644 11.646 3 11.1 3zM6.99805 18.998H9.00195V21.002H6.99805V18.998zM14.998 18.998H17.002V21.002H14.998V18.998zM13.002 20.998H10.998V23.002H13.002V20.998z"}}]},b=d({name:"RainLightFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:i,style:c}=O(r),p=a(()=>["t-icon","t-icon-rain-light-filled",i.value]),u=a(()=>s(s({},c.value),t.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:v})}}));return()=>y(h,f.value)}});export{b as default};
