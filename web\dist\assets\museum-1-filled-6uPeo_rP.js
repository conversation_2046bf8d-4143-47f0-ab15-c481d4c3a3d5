import{d,h as a,ab as m,ac as O,ad as y}from"./index-R826otwI.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){y(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var C={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M17 10.101V6.38122L17.0342 6.41379L18.4137 4.96572L17.6897 4.27596C17.3387 3.94161 16.9582 3.63781 16.5524 3.36887C15.2472 2.5038 13.6809 2 11.9999 2C10.319 2 8.75277 2.50375 7.44758 3.36874C7.04261 3.63712 6.66284 3.94021 6.31247 4.27374L5.58817 4.96322L6.96714 6.41182L7 6.38054V10.101C5.72948 8.80447 3.95869 8 2 8H1V22H11V15H13V22H23V8H22C20.0413 8 18.2705 8.80447 17 10.101ZM3 10.1C5.28224 10.5633 7 12.581 7 15V20H3V10.1ZM21 20H17V15C17 12.581 18.7178 10.5633 21 10.1V20ZM13.0039 6.99805V9.00195H11V6.99805H13.0039Z"}}]},g=d({name:"Museum1FilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:c}=m(t),u=a(()=>["t-icon","t-icon-museum-1-filled",l.value]),p=a(()=>s(s({},c.value),r.style)),f=a(()=>({class:u.value,style:p.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>O(C,f.value)}});export{g as default};
