import{d as O,h as a,ab as y,ac as d,ad as m}from"./index-CTFw1yDv.js";function l(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?l(Object(r),!0).forEach(function(t){m(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var b={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M3.43 7.6863L12.0003 18.3992L20.5707 7.68617C15.4379 4.10452 8.56279 4.10466 3.43 7.6863ZM1.37875 6.72498C7.57128 1.75848 16.4293 1.75824 22.6219 6.72485L23.401 7.34974L12.0003 21.6008L0.599609 7.34987L1.37875 6.72498Z"}}]},w=O({name:"Wifi1Icon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:i,style:c}=y(t),p=a(()=>["t-icon","t-icon-wifi-1",i.value]),u=a(()=>s(s({},c.value),r.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>d(b,f.value)}});export{w as default};
