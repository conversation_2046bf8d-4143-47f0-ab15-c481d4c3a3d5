import{d as f,h as a,ab as O,ac as y,ad as d}from"./index-CTFw1yDv.js";function o(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(C){return Object.getOwnPropertyDescriptor(e,C).enumerable})),r.push.apply(r,t)}return r}function i(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?o(Object(r),!0).forEach(function(t){d(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var b={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M9.01735 2.40327C9.84697 1.51548 10.8866 1.00001 12 1C13.1134 1.00001 14.153 1.51549 14.9826 2.40327C16.5862 4.1193 18.3279 6.57308 19.7793 9.18426C21.2271 11.7888 22.4277 14.6246 22.8919 17.1081C23.0423 17.9132 23.0609 18.736 22.7839 19.5399C22.5034 20.3537 21.9563 21.0435 21.1581 21.6429C20.7914 21.9183 20.2371 22.1003 19.7151 22.2351C19.1449 22.3824 18.4395 22.5143 17.6481 22.6253C16.0628 22.8477 14.0704 22.9948 12.0025 23H11.9975C9.92959 22.9948 7.93719 22.8477 6.35192 22.6253C5.56051 22.5143 4.85507 22.3824 4.28486 22.2351C3.76283 22.1003 3.20861 21.9183 2.84184 21.6429C2.04366 21.0435 1.49654 20.3537 1.21607 19.5399C0.939032 18.736 0.957642 17.9132 1.10811 17.1081C1.57226 14.6246 2.77291 11.7888 4.22063 9.18426C5.67204 6.57308 7.41375 4.1193 9.01735 2.40327ZM12 3C11.538 3.00001 11.0022 3.20856 10.4786 3.7688C9.00947 5.34096 7.3593 7.65421 5.96873 10.1559C4.57447 12.6643 3.48304 15.2873 3.07407 17.4755C2.96189 18.0758 2.98049 18.5213 3.10693 18.8882C3.22992 19.2451 3.48974 19.6283 4.0428 20.0436C4.03989 20.042 4.04061 20.042 4.0428 20.0436C4.05405 20.0501 4.1074 20.0804 4.22531 20.1259C4.36228 20.1788 4.54807 20.2374 4.78503 20.2986C5.25856 20.421 5.88463 20.5402 6.62976 20.6447C6.75051 20.6616 6.87398 20.6781 6.99999 20.6941V13H17V20.6941C17.126 20.6781 17.2495 20.6616 17.3702 20.6447C18.1154 20.5402 18.7414 20.421 19.2149 20.2986C19.4519 20.2374 19.6377 20.1788 19.7747 20.1259C19.8926 20.0804 19.945 20.0502 19.9563 20.0438C19.9585 20.0421 19.9592 20.0421 19.9563 20.0438C20.5094 19.6284 20.7701 19.2451 20.8931 18.8882C21.0195 18.5213 21.0381 18.0758 20.9259 17.4755C20.5169 15.2873 19.4255 12.6643 18.0312 10.1559C16.6407 7.65421 14.9905 5.34096 13.5214 3.7688C12.9978 3.20856 12.462 3.00001 12 3ZM15 20.8897V15H8.99999V20.8897C9.94562 20.9572 10.9614 20.9973 12 21C13.0386 20.9973 14.0544 20.9572 15 20.8897ZM10.9982 5.12813H13.0017V7.1321H10.9982V5.12813ZM8.2958 9.18694H10.2993V11.1909H8.2958V9.18694ZM13.7007 9.18694H15.7042V11.1909H13.7007V9.18694Z"}}]},g=f({name:"RiceBallIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:C,style:c}=O(t),s=a(()=>["t-icon","t-icon-rice-ball",C.value]),p=a(()=>i(i({},c.value),r.style)),u=a(()=>({class:s.value,style:p.value,onClick:v=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:v})}}));return()=>y(b,u.value)}});export{g as default};
