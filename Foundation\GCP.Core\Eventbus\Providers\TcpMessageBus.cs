using System.Collections.Concurrent;
using System.Text;
using GCP.Common;
using GCP.Eventbus.Infrastructure;
using Serilog;
using TouchSocket.Core;
using TouchSocket.Sockets;

namespace GCP.Eventbus.Providers
{
    class TcpMessageBus : MessageBusBase
    {
        private readonly ConcurrentDictionary<string, ConcurrentBag<TcpMessageConsumer>> _consumers;
        private readonly TcpService _tcpService;
        private readonly ConcurrentDictionary<string, TcpSessionClient> _clients; // 客户端管理
        private readonly TcpEventConfig _eventConfig; // 事件配置（在构造时初始化）
        private bool _isConnected;

        public override bool IsConnected => _isConnected;

        /// <summary>
        /// 是否支持自定义消息解析
        /// </summary>
        public bool SupportsCustomParsing => _eventConfig != null;

        /// <summary>
        /// 获取所有连接的客户端ID
        /// </summary>
        public List<string> GetClientIds()
        {
            return _tcpService.GetIds().ToList();
        }

        /// <summary>
        /// 向特定客户端发送响应消息
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <param name="message">消息内容</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        public async Task SendToClientAsync(string clientId, object message, CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();
            await EnsureConnectedAsync(cancellationToken);

            if (string.IsNullOrEmpty(clientId))
            {
                Log.Warning("客户端ID为空，无法发送消息");
                return;
            }

            if (!_tcpService.TryGetClient(clientId, out var client))
            {
                Log.Warning("找不到客户端 {ClientId}，可能已断开连接", clientId);
                return;
            }

            var json = JsonHelper.Serialize(message);

            try
            {
                await client.SendAsync(json);
                Log.Debug("成功向客户端 {ClientId} 发送消息: {Message}", clientId, json);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "向客户端 {ClientId} 发送消息失败", clientId);
                throw;
            }
        }

        /// <summary>
        /// 向特定客户端发送响应消息（通过客户端对象）
        /// </summary>
        /// <param name="client">客户端对象</param>
        /// <param name="message">消息内容</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        public async Task SendToClientAsync(TcpSessionClient client, object message, CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();

            if (client == null)
            {
                Log.Warning("客户端对象为空，无法发送消息");
                return;
            }

            var json = JsonHelper.Serialize(message);

            try
            {
                await client.SendAsync(json);
                Log.Debug("成功向客户端 {ClientId} 发送消息: {Message}", client.Id, json);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "向客户端 {ClientId} 发送消息失败", client.Id);
                throw;
            }
        }

        public TcpMessageBus(MessageBusOptions options)
            : base(options)
        {
            _consumers = new ConcurrentDictionary<string, ConcurrentBag<TcpMessageConsumer>>();
            _clients = new ConcurrentDictionary<string, TcpSessionClient>();
            _tcpService = new TcpService();
            _isConnected = false;

            // 在构造时初始化事件配置
            _eventConfig = InitializeEventConfig(options);

            // 配置TCP服务
            var config = new TouchSocketConfig()
                .SetListenIPHosts(Convert.ToInt32(options.Settings.GetValueOrDefault("Port", "7789")))
                .SetTcpDataHandlingAdapter(() => new JsonMessageAdapter()); // 添加JSON消息适配器

            _tcpService.SetupAsync(config);
            _tcpService.Received += OnTcpReceived;
            _tcpService.Connected += OnClientConnected;
            _tcpService.Closed += OnClientDisconnected;
        }

        private Task OnClientConnected(TcpSessionClient client, TouchSocketEventArgs e)
        {
            _clients[client.Id] = client;
            return Task.CompletedTask;
        }

        private Task OnClientDisconnected(TcpSessionClient client, TouchSocketEventArgs e)
        {
            _clients.TryRemove(client.Id, out _);
            return Task.CompletedTask;
        }

        private Task OnTcpReceived(TcpSessionClient client, ReceivedDataEventArgs e)
        {
            try
            {
                string message = null;

                // 检查是否有适配器处理的完整消息
                if (e.RequestInfo is JsonMessageInfo jsonInfo)
                {
                    message = jsonInfo.JsonMessage;
                    Log.Debug("通过适配器接收到完整JSON消息: {Message}", message);
                }
                else if (e.ByteBlock != null)
                {
                    // 如果没有适配器处理，直接从ByteBlock获取（可能不完整）
                    message = e.ByteBlock.ToString();
                    Log.Warning("直接从ByteBlock获取消息，可能存在分包问题: {Message}", message);
                }

                if (string.IsNullOrEmpty(message))
                {
                    Log.Debug("接收到空消息");
                    return Task.CompletedTask;
                }

                MessageEnvelope envelope = null;
                if (SupportsCustomParsing)
                {
                    envelope = ParseCustomMessage(message);
                }
                else
                {
                    envelope = JsonHelper.Deserialize<MessageEnvelope>(message);
                }

                if (envelope == null)
                {
                    Log.Warning("无法解析TCP消息: {Message}", message);
                    return Task.CompletedTask;
                }

                // 在消息头中添加客户端ID，以便后续回复
                envelope.Headers["ClientId"] = client.Id;
                envelope.Headers["Client"] = client; // 保存客户端引用以便直接回复

                var topic = envelope.Headers.TryGetValue("Topic", out var topicObj) ? topicObj?.ToString() : null;
                if (string.IsNullOrEmpty(topic))
                {
                    Log.Debug("消息缺少Topic信息: {Message}", message);
                    return Task.CompletedTask;
                }

                if (_consumers.TryGetValue(topic, out var consumers))
                {
                    // 并发地将消息派发给所有订阅者
                    var handlingTasks = consumers.Select(consumer => consumer.HandleMessageAsync(envelope, CancellationToken.None));

                    // 使用 ContinueWith 在所有任务完成后统一记录错误，避免阻塞接收线程
                    _ = Task.WhenAll(handlingTasks).ContinueWith(task =>
                    {
                        if (task.IsFaulted)
                        {
                            Log.Error(task.Exception, "One or more handlers failed while processing a message for topic {Topic}.", topic);
                        }
                    }, TaskContinuationOptions.OnlyOnFaulted);
                }
                else
                {
                    Log.Debug("没有找到Topic {Topic} 的消费者", topic);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "处理TCP消息时发生错误: {ErrorMessage}", ex.Message);
            }
            return Task.CompletedTask;
        }

        /// <summary>
        /// 在构造时初始化事件配置
        /// </summary>
        private TcpEventConfig InitializeEventConfig(MessageBusOptions options)
        {
            try
            {
                var port = Convert.ToInt32(options.Settings.GetValueOrDefault("Port", "7789"));
                var eventId = options.Settings.GetValueOrDefault("EventId", "");
                var eventName = options.Settings.GetValueOrDefault("EventName", "");
                var uniqueCodeField = options.Settings.GetValueOrDefault("UniqueCodeField", "");
                var contentField = options.Settings.GetValueOrDefault("ContentField", "");

                // 如果没有配置唯一编码字段，则返回null（表示不支持自定义解析）
                if (string.IsNullOrEmpty(uniqueCodeField))
                {
                    Log.Information("TCP事件 {EventName} 未配置唯一编码字段，将只支持标准MessageEnvelope格式", eventName);
                    return null;
                }

                Log.Information("TCP事件 {EventName} 配置: Port={Port}, UniqueCodeField={UniqueCodeField}, ContentField={ContentField}",
                    eventName, port, uniqueCodeField, contentField);

                return new TcpEventConfig
                {
                    EventId = eventId,
                    EventName = eventName,
                    Port = port,
                    UniqueCodeField = uniqueCodeField,
                    ContentField = contentField
                };
            }
            catch (Exception ex)
            {
                Log.Error(ex, "初始化TCP事件配置时发生错误");
                return null;
            }
        }

        /// <summary>
        /// 根据事件配置解析自定义消息格式
        /// </summary>
        private MessageEnvelope ParseCustomMessage(string message)
        {
            try
            {
                // 解析为通用的 JSON 对象
                var jsonData = JsonHelper.Deserialize<Dictionary<string, object>>(message);
                if (jsonData == null) return null;

                // 根据配置提取唯一编码和内容
                var uniqueCode = ExtractFieldValue(jsonData, _eventConfig.UniqueCodeField);
                var content = ExtractFieldValue(jsonData, _eventConfig.ContentField);

                if (string.IsNullOrEmpty(uniqueCode))
                {
                    Log.Debug("无法从消息中提取唯一编码，字段: {UniqueCodeField}, 消息: {Message}",
                        _eventConfig.UniqueCodeField, message);
                    return null;
                }

                // 构造 MessageEnvelope
                var envelope = new MessageEnvelope
                {
                    Payload = string.IsNullOrEmpty(content) ? message : $"{{\"_EVENT_DATA\": {content}}}", // 如果没有指定内容字段，使用原始消息
                    Headers = new Dictionary<string, object>
                    {
                        { "Topic", uniqueCode },
                        { "OriginalMessage", message }
                    }
                };

                Log.Debug("成功解析自定义TCP消息: UniqueCode={UniqueCode}, Topic={Topic}",
                    uniqueCode, _eventConfig.EventId);

                return envelope;
            }
            catch (Exception ex)
            {
                Log.Warning(ex, "解析自定义TCP消息失败: {Message}", message);
                return null;
            }
        }

        /// <summary>
        /// 从JSON数据中提取指定字段的值
        /// </summary>
        private string ExtractFieldValue(Dictionary<string, object> jsonData, string fieldPath)
        {
            if (string.IsNullOrEmpty(fieldPath) || jsonData == null) return null;

            // 支持嵌套字段，如 "device.id"
            var fieldParts = fieldPath.Split('.');
            object currentValue = jsonData;

            foreach (var part in fieldParts)
            {
                if (currentValue is IDictionary<string, object> dict && dict.TryGetValue(part, out var value))
                {
                    currentValue = value;
                }
                else
                {
                    return null;
                }
            }

            return currentValue?.ToString();
        }

        public override async Task ConnectAsync(CancellationToken cancellationToken = default)
        {
            await _tcpService.StartAsync();
            _isConnected = true;
        }

        public override async Task DisconnectAsync(CancellationToken cancellationToken = default)
        {
            await _tcpService.StopAsync(cancellationToken);
            _isConnected = false;
        }

        public override async Task PublishAsync(string topic, object message, IDictionary<string, object> headers = null, CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();
            await EnsureConnectedAsync(cancellationToken);

            var envelope = new MessageEnvelope
            {
                Payload = message,
                Headers = headers ?? new Dictionary<string, object>()
            };
            envelope.Headers["Topic"] = topic;

            var json = JsonHelper.Serialize(envelope);

            // 从 TcpService 直接获取所有在线的客户端，并发地向它们发送消息。
            var onlineClientIds = GetClientIds();
            var sendTasks = new List<Task>(onlineClientIds.Count());

            foreach (var clientId in onlineClientIds)
            {
                if (!_tcpService.TryGetClient(clientId, out var client)) { continue; }
                sendTasks.Add(client.SendAsync(json).ContinueWith(task =>
                {
                    if (task.IsFaulted)
                    {
                        Log.Warning(task.Exception, "Failed to send message to client {ClientId}", client.Id);
                    }
                }, cancellationToken));
            }

            await Task.WhenAll(sendTasks);
        }

        public override async Task PublishAsync(string topic, IEnumerable<object> messages, IDictionary<string, object> headers = null, CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();
            await EnsureConnectedAsync(cancellationToken);

            foreach (var message in messages)
            {
                await PublishAsync(topic, message, headers, cancellationToken);
            }
        }

        public override async Task SubscribeAsync(string topic, Func<MessageEnvelope, CancellationToken, Task> handler, ConsumerOptions options, CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();
            await EnsureConnectedAsync(cancellationToken);

            var consumer = new TcpMessageConsumer(options, handler);
            var consumers = _consumers.GetOrAdd(topic, _ => new ConcurrentBag<TcpMessageConsumer>());
            consumers.Add(consumer);
            await consumer.StartAsync(cancellationToken);
        }

        public override async Task SubscribeBatchAsync(string topic, Func<List<MessageEnvelope>, CancellationToken, Task> handler, ConsumerOptions options, CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();
            await EnsureConnectedAsync(cancellationToken);

            var consumer = new TcpMessageConsumer(options, handler);
            var consumers = _consumers.GetOrAdd(topic, _ => new ConcurrentBag<TcpMessageConsumer>());
            consumers.Add(consumer);
            await consumer.StartAsync(cancellationToken);
        }

        public override async Task UnsubscribeAsync(string topic, CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();

            if (_consumers.TryGetValue(topic, out var consumers))
            {
                var consumersToRemove = consumers.ToList();
                consumers.Clear();
                foreach (var consumer in consumersToRemove)
                {
                    await consumer.StopAsync(cancellationToken);
                    await consumer.DisposeAsync();
                }
            }
        }

        protected override async ValueTask DisposeAsyncCore()
        {
            if (IsDisposed) return;

            foreach (var consumers in _consumers.Values)
            {
                foreach (var consumer in consumers)
                {
                    await consumer.DisposeAsync();
                }
            }

            await _tcpService.StopAsync();
            _consumers.Clear();
            _clients.Clear();

            await base.DisposeAsyncCore();
        }
    }

    internal class TcpMessageConsumer : IMessageConsumer
    {
        private readonly Func<MessageEnvelope, CancellationToken, Task> _handler;
        private readonly Func<List<MessageEnvelope>, CancellationToken, Task> _batchHandler;
        private readonly CancellationTokenSource _cts;
        private bool _isRunning;

        public string Name { get; }
        public ConsumerOptions Options { get; }
        public bool IsRunning => _isRunning;

        public TcpMessageConsumer(ConsumerOptions options, Func<MessageEnvelope, CancellationToken, Task> handler)
        {
            Name = options.Name;
            Options = options;
            _handler = handler;
            _cts = new CancellationTokenSource();
        }

        public TcpMessageConsumer(ConsumerOptions options, Func<List<MessageEnvelope>, CancellationToken, Task> batchHandler)
        {
            Name = options.Name;
            Options = options;
            _batchHandler = batchHandler;
            _cts = new CancellationTokenSource();
        }

        public Task StartAsync(CancellationToken cancellationToken = default)
        {
            _isRunning = true;
            return Task.CompletedTask;
        }

        public Task StopAsync(CancellationToken cancellationToken = default)
        {
            _isRunning = false;
            _cts.Cancel();
            return Task.CompletedTask;
        }

        public async ValueTask DisposeAsync()
        {
            await StopAsync();
            _cts.Dispose();
        }

        public async Task HandleMessageAsync(MessageEnvelope envelope, CancellationToken cancellationToken)
        {
            if (!_isRunning) return;
            if (_cts.IsCancellationRequested) return;

            using var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(_cts.Token, cancellationToken);
            try
            {
                if (_handler != null)
                {
                    await _handler(envelope, linkedCts.Token);
                }
                else if (_batchHandler != null)
                {
                    await _batchHandler([envelope], linkedCts.Token);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "处理消息时发生错误");
            }
        }
    }

    /// <summary>
    /// TCP事件配置
    /// </summary>
    internal class TcpEventConfig
    {
        public string EventId { get; set; }
        public string EventName { get; set; }
        public int Port { get; set; }
        public string UniqueCodeField { get; set; }
        public string ContentField { get; set; }
    }

    /// <summary>
    /// TCP消息响应帮助类
    /// </summary>
    internal static class TcpMessageResponseHelper
    {
        /// <summary>
        /// 从消息信封中提取客户端ID
        /// </summary>
        /// <param name="envelope">消息信封</param>
        /// <returns>客户端ID</returns>
        public static string GetClientId(this MessageEnvelope envelope)
        {
            return envelope.Headers.TryGetValue("ClientId", out var clientId) ? clientId?.ToString() : null;
        }

        /// <summary>
        /// 从消息信封中提取客户端对象
        /// </summary>
        /// <param name="envelope">消息信封</param>
        /// <returns>客户端对象</returns>
        public static TcpSessionClient GetClient(this MessageEnvelope envelope)
        {
            return envelope.Headers.TryGetValue("Client", out var client) ? client as TcpSessionClient : null;
        }

        /// <summary>
        /// 向消息的发送方回复响应
        /// </summary>
        /// <param name="messageBus">TCP消息总线</param>
        /// <param name="originalMessage">原始消息</param>
        /// <param name="responseTopic">响应主题</param>
        /// <param name="responseData">响应数据</param>
        /// <param name="headers">额外的消息头</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        public static async Task ReplyToSenderAsync(this TcpMessageBus messageBus, MessageEnvelope originalMessage, object responseData,
            CancellationToken cancellationToken = default)
        {
            var client = originalMessage.GetClient();
            if (client != null)
            {
                await messageBus.SendToClientAsync(client, responseData, cancellationToken);
            }
            else
            {
                var clientId = originalMessage.GetClientId();
                if (!string.IsNullOrEmpty(clientId))
                {
                    await messageBus.SendToClientAsync(clientId, responseData, cancellationToken);
                }
                else
                {
                    Log.Warning("无法回复消息：原始消息中没有客户端信息");
                }
            }
        }

        /// <summary>
        /// 创建成功响应
        /// </summary>
        /// <param name="data">响应数据</param>
        /// <param name="message">响应消息</param>
        /// <returns></returns>
        public static object CreateSuccessResponse(object data = null, string message = "操作成功")
        {
            return new
            {
                Success = true,
                Message = message,
                Data = data,
                Timestamp = DateTime.UtcNow
            };
        }

        /// <summary>
        /// 创建错误响应
        /// </summary>
        /// <param name="errorMessage">错误消息</param>
        /// <param name="errorCode">错误代码</param>
        /// <returns></returns>
        public static object CreateErrorResponse(string errorMessage, string errorCode = null)
        {
            return new
            {
                Success = false,
                Message = errorMessage,
                ErrorCode = errorCode,
                Timestamp = DateTime.UtcNow
            };
        }
    }

    /// <summary>
    /// JSON消息信息类，用于适配器传递完整的JSON消息
    /// </summary>
    internal class JsonMessageInfo : IRequestInfo
    {
        public string JsonMessage { get; set; }
    }

    /// <summary>
    /// JSON消息适配器，用于处理TCP分包/粘包问题
    /// 基于JSON的大括号匹配来识别完整的JSON消息
    /// </summary>
    internal class JsonMessageAdapter : SingleStreamDataHandlingAdapter
    {
        private readonly StringBuilder _messageBuffer = new StringBuilder();
        private int _braceCount = 0;
        private bool _inString = false;
        private bool _escapeNext = false;

        public override bool CanSendRequestInfo => false;
        public override bool CanSplicingSend => false;

        protected override async Task PreviewReceivedAsync(ByteBlock byteBlock)
        {
            try
            {
                var receivedText = Encoding.UTF8.GetString(byteBlock.TotalMemory.GetArray().Array, 0, byteBlock.Length);
                Log.Debug("JSON适配器接收到数据: {ReceivedText}", receivedText);

                // 将接收到的文本添加到缓冲区
                _messageBuffer.Append(receivedText);

                // 尝试从缓冲区中提取完整的JSON消息
                await ProcessBufferedData();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "JSON消息适配器处理数据时发生错误");
                // 重置状态
                _messageBuffer.Clear();
                _braceCount = 0;
                _inString = false;
                _escapeNext = false;
            }
        }

        private async Task ProcessBufferedData()
        {
            var bufferContent = _messageBuffer.ToString();
            var startIndex = 0;

            while (startIndex < bufferContent.Length)
            {
                var jsonStart = -1;
                var jsonEnd = -1;
                var braceCount = 0;
                var inString = false;
                var escapeNext = false;

                // 寻找JSON的开始位置
                for (int i = startIndex; i < bufferContent.Length; i++)
                {
                    var c = bufferContent[i];

                    if (c == '{' && !inString)
                    {
                        if (jsonStart == -1)
                        {
                            jsonStart = i;
                        }
                        braceCount++;
                    }
                    else if (c == '}' && !inString)
                    {
                        braceCount--;
                        if (braceCount == 0 && jsonStart != -1)
                        {
                            jsonEnd = i;
                            break;
                        }
                    }
                    else if (c == '"' && !escapeNext)
                    {
                        inString = !inString;
                    }

                    escapeNext = (c == '\\' && inString && !escapeNext);
                }

                // 如果找到完整的JSON消息
                if (jsonStart != -1 && jsonEnd != -1)
                {
                    var jsonLength = jsonEnd - jsonStart + 1;
                    var completeJson = bufferContent.Substring(jsonStart, jsonLength);

                    Log.Debug("提取到完整JSON消息: {JsonMessage}", completeJson);

                    // 发送完整的JSON消息
                    var jsonInfo = new JsonMessageInfo { JsonMessage = completeJson };
                    await this.GoReceivedAsync(null, jsonInfo);

                    // 移动到下一个可能的消息位置
                    startIndex = jsonEnd + 1;
                }
                else
                {
                    // 没有找到完整的JSON，保留剩余数据等待更多数据
                    if (jsonStart != -1)
                    {
                        // 有开始但没有结束，保留从开始位置的数据
                        var remainingData = bufferContent.Substring(jsonStart);
                        _messageBuffer.Clear();
                        _messageBuffer.Append(remainingData);
                        Log.Debug("保留不完整的JSON数据等待更多数据: {RemainingData}", remainingData);
                    }
                    else
                    {
                        // 没有找到JSON开始，清空缓冲区
                        _messageBuffer.Clear();
                        Log.Debug("未找到JSON开始标记，清空缓冲区");
                    }
                    break;
                }
            }

            // 如果所有数据都处理完了，清空缓冲区
            if (startIndex >= bufferContent.Length)
            {
                _messageBuffer.Clear();
            }
        }

        protected override Task PreviewSendAsync(ReadOnlyMemory<byte> memory)
        {
            // 直接发送数据，不做额外处理
            return this.GoSendAsync(memory);
        }

        protected override async Task PreviewSendAsync(IList<ArraySegment<byte>> transferBytes)
        {
            // 拼接发送模式，需要将多个片段合并后发送
            var totalLength = transferBytes.Sum(segment => segment.Count);
            using var byteBlock = new ByteBlock(totalLength);

            foreach (var segment in transferBytes)
            {
                byteBlock.Write(new ReadOnlySpan<byte>(segment.Array, segment.Offset, segment.Count));
            }

            await this.GoSendAsync(byteBlock.Memory);
        }

        protected override Task PreviewSendAsync(IRequestInfo requestInfo)
        {
            // 不支持发送RequestInfo对象
            throw new NotSupportedException("JsonMessageAdapter不支持发送IRequestInfo对象");
        }
    }
}