import{d,h as a,ab as O,ac as y,ad as V}from"./index-CTFw1yDv.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function c(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){V(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var H={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M13 3.12602V2H11V3.12602C9.27477 3.57006 8 5.13616 8 7H5V9H6V10H18V9H19V7H16C16 5.13616 14.7252 3.57006 13 3.12602zM2 12H22V14H21V20H22V22H16V20C16 17.7909 14.2091 16 12 16 9.79086 16 8 17.7909 8 20V22H2V20H3V14H2V12z"}},{tag:"path",attrs:{fill:"currentColor",d:"M10 20C10 18.8954 10.8954 18 12 18C13.1046 18 14 18.8954 14 20V22H10V20Z"}}]},m=d({name:"PalaceFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:l,style:s}=O(r),p=a(()=>["t-icon","t-icon-palace-filled",l.value]),u=a(()=>c(c({},s.value),t.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>y(H,f.value)}});export{m as default};
