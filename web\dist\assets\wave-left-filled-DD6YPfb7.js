import{d,h as a,ab as C,ac as O,ad as y}from"./index-CTFw1yDv.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){y(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M7.07996 3.01688L6.25658 3.58437C5.25048 4.27779 4.37742 5.15081 3.68397 6.15688L3.11645 6.98024 1.46973 5.8452 2.03725 5.02184C2.86875 3.81549 3.91522 2.76906 5.1216 1.9376L5.94498 1.37012 7.07996 3.01688zM10.2448 4.46556C10.7819 3.92867 11.6529 3.92995 12.1885 4.4684L17.4423 9.75065 16.9979 8.65493C16.4384 7.27525 18.0035 6.00498 19.2385 6.83649 19.4269 6.96332 19.5842 7.13103 19.6987 7.32712L22.0783 11.4019C23.7259 14.2233 23.2639 17.8009 20.9536 20.1111L20.1093 20.9554C17.3017 23.7631 12.7495 23.7631 9.94188 20.9554L4.90742 15.9198C4.37073 15.383 4.37056 14.5128 4.90705 13.9758 5.44395 13.4384 6.31495 13.4382 6.85213 13.9753L8.45572 15.5789 8.98679 15.0478 5.29669 11.3577C4.75982 10.8209 4.75981 9.95045 5.29666 9.41357 5.83353 8.87668 6.70399 8.87667 7.24088 9.41355L10.931 13.1037 11.4613 12.5733 6.40446 7.51647C5.86764 6.97965 5.86747 6.10934 6.40408 5.57231 6.94099 5.03498 7.81188 5.0348 8.349 5.57192L13.4059 10.6288 13.9361 10.0986 10.2446 6.4071C9.70839 5.87093 9.7085 5.0016 10.2448 4.46556z"}}]},g=d({name:"WaveLeftFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:l,style:c}=C(r),p=a(()=>["t-icon","t-icon-wave-left-filled",l.value]),f=a(()=>s(s({},c.value),t.style)),u=a(()=>({class:p.value,style:f.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>O(m,u.value)}});export{g as default};
