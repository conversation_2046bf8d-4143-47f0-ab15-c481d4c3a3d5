import{d,h as a,ab as O,ac as y,ad as m}from"./index-R826otwI.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){m(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var g={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M7.00391 10.0041V8.00015H5.00391V8.00406H5V10.0041H7.00391Z"}},{tag:"path",attrs:{fill:"currentColor",d:"M10 5.58594L6 1.58594L2 5.58594V22.0002L11.5 22V15H18.5V22H22V9.99561H20.9751C20.7275 7.49954 18.6607 5.48522 16 5.07605V3.5H14V5.07605C12.3332 5.33238 10.8994 6.21866 10 7.45869V5.58594ZM7.99609 20.0002H4V6.41437L6 4.41436L8 6.41436L7.99609 20.0002Z"}},{tag:"path",attrs:{fill:"currentColor",d:"M13.5 17V22H16.5V17H13.5Z"}}]},b=d({name:"Mosque1FilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:l,style:c}=O(r),p=a(()=>["t-icon","t-icon-mosque-1-filled",l.value]),u=a(()=>s(s({},c.value),t.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>y(g,f.value)}});export{b as default};
