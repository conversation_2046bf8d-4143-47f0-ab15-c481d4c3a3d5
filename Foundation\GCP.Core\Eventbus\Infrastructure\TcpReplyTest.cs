using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using GCP.Common;
using GCP.Eventbus.Infrastructure;
using GCP.Eventbus.Providers;
using Serilog;
using TouchSocket.Sockets;

namespace GCP.Eventbus.Infrastructure
{
    /// <summary>
    /// TCP自动回复功能测试
    /// </summary>
    public class TcpReplyTest
    {
        /// <summary>
        /// 测试TCP消息自动回复功能
        /// </summary>
        public static async Task TestTcpAutoReply()
        {
            Log.Information("=== TCP自动回复功能测试 ===");

            // 1. 创建TCP消息总线
            var messageBus = CreateTcpMessageBus();
            await messageBus.ConnectAsync();

            // 2. 创建消息消费者
            var consumer = CreateMessageConsumer(messageBus);
            await consumer.StartAsync();

            // 3. 模拟TCP客户端消息
            await SimulateTcpClientMessage(messageBus);

            Log.Information("测试完成");
        }

        /// <summary>
        /// 创建TCP消息总线
        /// </summary>
        private static TcpMessageBus CreateTcpMessageBus()
        {
            var options = new MessageBusOptions
            {
                Settings = new Dictionary<string, string>
                {
                    ["Port"] = "7789",
                    ["EventName"] = "TcpReplyTestEvent"
                }
            };

            return new TcpMessageBus(options);
        }

        /// <summary>
        /// 创建消息消费者
        /// </summary>
        private static MessageConsumer CreateMessageConsumer(IMessageBus messageBus)
        {
            var consumerOptions = new ConsumerOptions
            {
                Name = "TcpReplyTestConsumer",
                Topic = "test.function",
                ConsumerId = "TestFunction", // 这应该对应一个实际的函数路径
                Settings = new Dictionary<string, string>()
            };

            return new MessageConsumer(messageBus, consumerOptions);
        }

        /// <summary>
        /// 模拟TCP客户端发送消息
        /// </summary>
        private static async Task SimulateTcpClientMessage(TcpMessageBus messageBus)
        {
            // 创建模拟的TCP客户端
            var mockClient = CreateMockTcpClient();

            // 构建测试消息
            var testMessage = new MessageEnvelope
            {
                Payload = new
                {
                    userId = "test_user_123",
                    action = "getUserInfo",
                    parameters = new { includeDetails = true }
                },
                Headers = new Dictionary<string, object>
                {
                    ["Topic"] = "test.function",
                    ["MessageId"] = Guid.NewGuid().ToString(),
                    ["RequestId"] = "test_req_001",
                    ["ClientId"] = mockClient.Id,
                    ["Client"] = mockClient // 关键：添加客户端对象
                }
            };

            Log.Information("发送测试消息: {Message}", JsonHelper.Serialize(testMessage.Payload));

            // 发布消息到消息总线
            await messageBus.PublishAsync("test.function", testMessage.Payload, testMessage.Headers);

            // 等待处理完成
            await Task.Delay(2000);
        }

        /// <summary>
        /// 创建模拟的TCP客户端
        /// </summary>
        private static MockTcpSessionClient CreateMockTcpClient()
        {
            return new MockTcpSessionClient("test_client_001");
        }
    }

    /// <summary>
    /// 模拟的TCP会话客户端（用于测试）
    /// </summary>
    public class MockTcpSessionClient : TcpSessionClient
    {
        private readonly string _clientId;
        private readonly List<string> _sentMessages = new List<string>();

        public MockTcpSessionClient(string clientId)
        {
            _clientId = clientId;
        }

        public override string Id => _clientId;

        public List<string> SentMessages => _sentMessages;

        public override async Task SendAsync(ReadOnlyMemory<byte> memory)
        {
            var message = System.Text.Encoding.UTF8.GetString(memory.Span);
            _sentMessages.Add(message);
            
            Log.Information("模拟TCP客户端收到响应: {Response}", message);
            
            // 解析响应消息
            try
            {
                var envelope = JsonHelper.Deserialize<MessageEnvelope>(message);
                var payload = JsonHelper.Deserialize<Dictionary<string, object>>(envelope.Payload.ToString());
                
                if (payload.TryGetValue("Success", out var success) && (bool)success)
                {
                    Log.Information("✅ 函数执行成功");
                    if (payload.TryGetValue("Data", out var data))
                    {
                        Log.Information("📄 返回数据: {Data}", JsonHelper.Serialize(data));
                    }
                    if (payload.TryGetValue("ProcessingTimeMs", out var time))
                    {
                        Log.Information("⏱️ 处理耗时: {Time}ms", time);
                    }
                }
                else
                {
                    Log.Warning("❌ 函数执行失败");
                    if (payload.TryGetValue("Message", out var errorMsg))
                    {
                        Log.Warning("💬 错误信息: {ErrorMessage}", errorMsg);
                    }
                    if (payload.TryGetValue("ErrorType", out var errorType))
                    {
                        Log.Warning("🏷️ 错误类型: {ErrorType}", errorType);
                    }
                }

                // 显示响应头信息
                Log.Information("📋 响应头信息:");
                foreach (var header in envelope.Headers)
                {
                    if (header.Key != "Client") // 跳过客户端对象
                    {
                        Log.Information("   {Key}: {Value}", header.Key, header.Value);
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "解析响应消息失败: {Message}", message);
            }

            await Task.CompletedTask;
        }

        public override Task SendAsync(string message)
        {
            var bytes = System.Text.Encoding.UTF8.GetBytes(message);
            return SendAsync(bytes);
        }

        // 其他必要的重写方法（简化实现）
        public override bool IsConnected => true;
        public override DateTime LastReceivedTime => DateTime.Now;
        public override DateTime LastSentTime => DateTime.Now;
    }

    /// <summary>
    /// 测试函数示例
    /// </summary>
    public static class TestFunctions
    {
        /// <summary>
        /// 模拟用户查询函数
        /// </summary>
        public static async Task<object> GetUserInfo(Dictionary<string, object> args)
        {
            // 模拟处理延迟
            await Task.Delay(100);

            if (args.TryGetValue("userId", out var userIdObj))
            {
                var userId = userIdObj.ToString();
                
                // 模拟查询结果
                return new
                {
                    UserId = userId,
                    UserName = $"User_{userId}",
                    Email = $"{userId}@example.com",
                    CreateTime = DateTime.Now,
                    IsActive = true,
                    Profile = new
                    {
                        Age = 25,
                        City = "北京",
                        Department = "技术部"
                    }
                };
            }

            throw new ArgumentException("缺少userId参数");
        }

        /// <summary>
        /// 模拟会抛出异常的函数
        /// </summary>
        public static async Task<object> ErrorFunction(Dictionary<string, object> args)
        {
            await Task.Delay(50);
            throw new InvalidOperationException("这是一个测试异常");
        }
    }
}
