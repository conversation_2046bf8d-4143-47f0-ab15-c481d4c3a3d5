import{R as i}from"./index-CqfrbUmy.js";import{d as p,f as u,b as d,o as m,n as r,a as t,g as o,p as c,t as n,aT as l,_ as b}from"./index-CTFw1yDv.js";const g={class:"result-slot-container"},_={class:"recommend-container"},w={class:"recommend-browser"},f={name:"ResultBrowserIncompatible"},v=p({...f,setup(I){return(s,e)=>{const a=u("t-button");return m(),d(i,{title:s.t("pages.result.browserIncompatible.title"),type:"ie",tip:s.t("pages.result.browserIncompatible.subtitle")},{default:r(()=>[t("div",g,[o(a,{class:"result-button",onClick:e[0]||(e[0]=()=>s.$router.push("/"))},{default:r(()=>[c(n(s.t("pages.result.browserIncompatible.back")),1)]),_:1}),t("div",_,[t("div",null,n(s.t("pages.result.browserIncompatible.recommend")),1),t("div",w,[t("div",null,[o(l,{class:"browser-icon",url:"https://tdesign.gtimg.com/starter/result-page/chorme.png"}),e[1]||(e[1]=t("div",null,"Chrome",-1))]),t("div",null,[o(l,{class:"browser-icon",url:"https://tdesign.gtimg.com/starter/result-page/qq-browser.png"}),e[2]||(e[2]=t("div",null,"QQ Browser",-1))])])])])]),_:1},8,["title","tip"])}}}),C=b(v,[["__scopeId","data-v-60632563"]]);export{C as default};
