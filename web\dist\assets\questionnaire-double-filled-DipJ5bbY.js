import{d as C,h as a,ab as d,ac as O,ad as y}from"./index-CTFw1yDv.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){y(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var b={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M20 2V16H5.82046L1 19.4432V2H20ZM9.5 7.5C9.5 6.94772 9.94771 6.5 10.5 6.5C11.0523 6.5 11.5 6.94772 11.5 7.5C11.5 7.82392 11.4223 8.00753 11.3501 8.11822C11.2729 8.23684 11.1597 8.3355 11.0062 8.42305C10.8462 8.51433 10.6775 8.57513 10.4869 8.64144L10.4686 8.64778C10.3935 8.67378 10.2666 8.71774 10.1602 8.76828C10.1042 8.79486 9.98358 8.85498 9.86436 8.96014C9.74806 9.06273 9.50345 9.3263 9.50345 9.75V10.75H11.5034V10.3975C11.6536 10.3366 11.8243 10.2589 11.9972 10.1603C12.3429 9.96311 12.7288 9.66594 13.0259 9.2099C13.3282 8.74594 13.5 8.17608 13.5 7.5C13.5 5.84315 12.1569 4.5 10.5 4.5C8.84315 4.5 7.5 5.84315 7.5 7.5V8.5H9.5V7.5ZM11.5039 11.5H9.5V13.5039H11.5039V11.5Z"}},{tag:"path",attrs:{fill:"currentColor",d:"M23.5 22.9432V4.5H21.5V19.0568L19.3205 17.5H7.5V19.5H18.6795L23.5 22.9432Z"}}]},m=C({name:"QuestionnaireDoubleFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:l,style:c}=d(r),u=a(()=>["t-icon","t-icon-questionnaire-double-filled",l.value]),p=a(()=>s(s({},c.value),t.style)),f=a(()=>({class:u.value,style:p.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>O(b,f.value)}});export{m as default};
