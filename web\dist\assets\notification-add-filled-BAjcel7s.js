import{d as v,h as a,ab as O,ac as y,ad as m}from"./index-R826otwI.js";function l(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),t.push.apply(t,r)}return t}function c(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?l(Object(t),!0).forEach(function(r){m(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):l(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var b={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M4 8C4 3.58172 7.58172 0 12 0 16.4183 0 20 3.58172 20 8V12.6972L22 15.6972V20H2V15.6972L4 12.6972V8zM13 9L13 6 11 6 11 9 8 9 8 11 11 11V14H13V11H16V9H13zM8.64453 21C9.07481 22.4457 10.4141 23.5 11.9996 23.5 13.5851 23.5 14.9244 22.4457 15.3547 21H8.64453z"}}]},P=v({name:"NotificationAddFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:i,style:s}=O(r),p=a(()=>["t-icon","t-icon-notification-add-filled",i.value]),f=a(()=>c(c({},s.value),t.style)),u=a(()=>({class:p.value,style:f.value,onClick:d=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:d})}}));return()=>y(b,u.value)}});export{P as default};
