import{d,h as a,ab as O,ac as y,ad as m}from"./index-R826otwI.js";function l(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function c(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?l(Object(r),!0).forEach(function(t){m(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var b={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M4 8C4 3.58172 7.58172 0 12 0 16.4183 0 20 3.58172 20 8V12.6972L22 15.6972V20H2V15.6972L4 12.6972V8zM11 6V12H13V6H11zM13.0039 13.5H11V15.5039H13.0039V13.5zM8.64453 21C9.07481 22.4457 10.4141 23.5 11.9996 23.5 13.5851 23.5 14.9244 22.4457 15.3547 21H8.64453z"}}]},P=d({name:"NotificationErrorFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:o,style:s}=O(t),p=a(()=>["t-icon","t-icon-notification-error-filled",o.value]),f=a(()=>c(c({},s.value),r.style)),u=a(()=>({class:p.value,style:f.value,onClick:v=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:v})}}));return()=>y(b,u.value)}});export{P as default};
