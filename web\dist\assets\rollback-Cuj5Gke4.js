import{d as O,h as a,ab as y,ac as b,ad as d}from"./index-R826otwI.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function c(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){d(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M20.9293 13.9999C20.444 17.3922 17.5266 19.9999 14.0001 19.9999H5.50015L5.50015 17.9999H14.0002C16.7616 17.9999 19.0001 15.7613 19.0001 12.9999C19.0001 10.2385 16.7616 7.99991 14.0001 7.99991L6.91436 7.99991L9.41436 10.4999L8.00015 11.9141L3.08594 6.99991L8.00015 2.08569L9.41436 3.49991L6.91436 5.99991L14.0001 5.99991C17.8661 5.99991 21.0001 9.13392 21.0001 12.9999V13.9999H20.9293Z"}}]},C=O({name:"RollbackIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:s}=y(t),p=a(()=>["t-icon","t-icon-rollback",l.value]),u=a(()=>c(c({},s.value),r.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:f})}}));return()=>b(m,v.value)}});export{C as default};
