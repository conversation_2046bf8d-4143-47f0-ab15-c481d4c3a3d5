import{g as j}from"./detail-CX3CCOJt.js";import{q as t,d as H,r as y,i as J,f as c,c as u,o as r,g as a,n,a as d,F as k,s as N,u as l,t as i,y as A,e as C,p,b as x,_ as Q}from"./index-R826otwI.js";import W from"./Product-B7n-3dP_.js";const I=[{name:t("constants.contract.name"),value:"总部办公用品采购项目",type:null},{name:t("constants.contract.status"),value:"履行中",type:{key:"contractStatus",value:"inProgress"}},{name:t("constants.contract.num"),value:"BH00010",type:null},{name:t("constants.contract.type"),value:t("constants.contract.typeOptions.main"),type:null},{name:t("constants.contract.payType"),value:t("constants.contract.pay"),type:null},{name:t("constants.contract.amount"),value:"¥ 5,000,000",type:null},{name:t("constants.contract.company"),value:"腾讯科技（深圳）有限公司",type:null},{name:t("constants.contract.employee"),value:"欧尚",type:null},{name:t("constants.contract.signDate"),value:"2020-12-20",type:null},{name:t("constants.contract.effectiveDate"),value:"2021-01-20",type:null},{name:t("constants.contract.endDate"),value:"2022-12-20",type:null},{name:t("constants.contract.attachment"),value:"总部办公用品采购项目合同.pdf",type:{key:"contractAnnex",value:"pdf"}},{name:t("constants.contract.remark"),value:"--",type:null},{name:t("constants.contract.createDate"),value:"2020-12-22 10:00:00",type:null}],X=[{name:"MacBook Pro 2021",subtitle:"苹果公司（Apple Inc. ）",size:"13.3 英寸",cpu:"Apple M1",memory:"RAM 16GB",info:"最高可选配 16GB 内存 · 最高可选配 2TB 存储设备 电池续航最长达 18 小时",use:1420,stock:1500},{name:"Surface Laptop Go",subtitle:"微软（Microsoft Corporation）",size:"12.4 英寸",cpu:"Core i7",memory:"RAM 16GB",info:"常规使用 Surface，续航时间最长可达13小时 随时伴您工作",use:120,stock:2e3}],Y={class:"detail-advanced"},Z={class:"info-block"},tt={key:0},et={class:"product-add"},at={class:"product-sub"},nt={class:"dialog-info-block"},lt={key:0},ot={name:"DetailAdvanced"},st=H({...ot,setup(ct){const z=[{width:280,ellipsis:!0,colKey:"index",title:t("pages.detailCard.detail.form.applyNo"),sorter:(o,s)=>o.index.substr(3)-s.index.substr(3)},{width:200,ellipsis:!0,colKey:"pdName",title:t("pages.detailCard.detail.form.product"),sorter:(o,s)=>o.pdName.length-s.pdName.length},{width:200,ellipsis:!0,colKey:"pdNum",title:t("pages.detailCard.detail.form.productNo")},{width:160,ellipsis:!0,colKey:"purchaseNum",title:t("pages.detailCard.detail.form.num"),sorter:(o,s)=>o.purchaseNum-s.purchaseNum},{width:160,ellipsis:!0,colKey:"adminName",title:t("pages.detailCard.detail.form.department")},{width:200,ellipsis:!0,colKey:"updateTime",title:t("pages.detailCard.detail.form.createTime"),sorter:(o,s)=>Date.parse(o.updateTime)-Date.parse(s.updateTime)},{align:"left",fixed:"right",width:200,className:"test2",colKey:"op",title:t("pages.detailCard.detail.form.operation")}],h=y([]),b=y({defaultPageSize:10,total:100,defaultCurrent:1}),_=y(0),K=()=>{setInterval(()=>{_.value>5&&(_.value=-1),_.value+=1},2e3)},V=async()=>{try{const{list:o}=await j();h.value=o,b.value={...b.value,total:o.length}}catch(o){console.log(o)}};J(()=>{K(),V()});const f=y(!1),M=o=>{console.log(o)},G=(o,s)=>{console.log("统一Change",o,s)},L=()=>{f.value=!0},O=o=>{h.value.splice(o.rowIndex,1)},F=()=>{f.value=!1};return(o,s)=>{const g=c("t-card"),v=c("t-step-item"),R=c("t-steps"),T=c("t-row"),w=c("t-radio-button"),U=c("t-radio-group"),B=c("t-icon"),D=c("t-col"),P=c("t-tag"),S=c("t-link"),q=c("t-space"),E=c("t-table"),$=c("t-dialog");return r(),u("div",Y,[a(g,{title:l(t)("pages.detailCard.baseInfo.title"),bordered:!1},{default:n(()=>[d("div",Z,[(r(!0),u(k,null,N(l(I),(e,m)=>(r(),u("div",{key:m,class:"info-item"},[d("h1",null,i(e.name),1),d("span",{class:A({inProgress:e.type&&e.type.value==="inProgress",pdf:e.type&&e.type.value==="pdf"})},[e.type&&e.type.key==="contractStatus"?(r(),u("i",tt)):C("",!0),p(" "+i(e.value),1)],2)]))),128))])]),_:1},8,["title"]),a(g,{title:l(t)("pages.detailCard.invoice.title"),class:"container-base-margin-top",bordered:!1},{default:n(()=>[a(T,{justify:"space-between"},{default:n(()=>[a(R,{current:_.value},{default:n(()=>[a(v,{title:l(t)("pages.detailCard.invoice.step1.title"),content:l(t)("pages.detailCard.invoice.step1.content")},null,8,["title","content"]),a(v,{title:l(t)("pages.detailCard.invoice.step2.title"),content:l(t)("pages.detailCard.invoice.step2.content")},null,8,["title","content"]),a(v,{title:l(t)("pages.detailCard.invoice.step3.title"),content:l(t)("pages.detailCard.invoice.step3.content")},null,8,["title","content"]),a(v,{title:l(t)("pages.detailCard.invoice.step4.title")},null,8,["title"])]),_:1},8,["current"])]),_:1})]),_:1},8,["title"]),a(g,{title:l(t)("pages.detailCard.product.title"),class:"container-base-margin-top",bordered:!1},{actions:n(()=>[a(U,{"default-value":"dateVal"},{default:n(()=>[a(w,{value:"dateVal"},{default:n(()=>[p(i(l(t)("pages.detailCard.product.quarter")),1)]),_:1}),a(w,{value:"monthVal"},{default:n(()=>[p(i(l(t)("pages.detailCard.product.month")),1)]),_:1})]),_:1})]),default:n(()=>[a(T,{gutter:16,class:"product-block-container"},{default:n(()=>[a(D,{xl:4},{default:n(()=>[d("div",et,[d("div",at,[a(B,{name:"add",class:"product-sub-icon"}),d("span",null,i(l(t)("pages.detailCard.product.add")),1)])])]),_:1}),(r(!0),u(k,null,N(l(X),(e,m)=>(r(),x(D,{key:m,xl:4},{default:n(()=>[a(W,{data:e},null,8,["data"])]),_:2},1024))),128))]),_:1})]),_:1},8,["title"]),a(g,{title:l(t)("pages.detailCard.detail.title"),class:"container-base-margin-top",bordered:!1},{default:n(()=>[a(E,{columns:z,data:h.value,pagination:b.value,hover:!0,stripe:!0,"row-key":"index",size:"large",onSortChange:M,onChange:G},{pdName:n(({row:e})=>[d("span",null,[p(i(e.pdName)+" ",1),e.pdType?(r(),x(P,{key:0,size:"medium",style:{"margin-left":"var(--td-comp-margin-s)"}},{default:n(()=>[p(i(e.pdType),1)]),_:2},1024)):C("",!0)])]),purchaseNum:n(({row:e})=>[d("span",null,[p(i(e.purchaseNum)+" ",1),e.purchaseNum>50?(r(),x(P,{key:0,theme:"danger",variant:"light",size:"medium",style:{"margin-left":"var(--td-comp-margin-s)"}},{default:n(()=>s[2]||(s[2]=[p("超预算",-1)])),_:1,__:[2]})):C("",!0)])]),op:n(e=>[a(q,null,{default:n(()=>[a(S,{theme:"primary",onClick:s[0]||(s[0]=m=>L())},{default:n(()=>[p(i(l(t)("pages.detailCard.detail.form.manage")),1)]),_:1}),a(S,{theme:"danger",onClick:m=>O(e)},{default:n(()=>[p(i(l(t)("pages.detailCard.detail.form.delete")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),"op-column":n(()=>[a(B,{name:"descending-order"})]),_:1},8,["data","pagination"])]),_:1},8,["title"]),a($,{visible:f.value,"onUpdate:visible":s[1]||(s[1]=e=>f.value=e),header:l(t)("pages.detailCard.baseInfo.title"),onConfirm:F},{body:n(()=>[d("div",nt,[(r(!0),u(k,null,N(l(I),(e,m)=>(r(),u("div",{key:m,class:"info-item"},[d("h1",null,i(e.name),1),d("span",{class:A({inProgress:e.type&&e.type.value==="inProgress",pdf:e.type&&e.type.value==="pdf"})},[e.type&&e.type.key==="contractStatus"?(r(),u("i",lt)):C("",!0),p(" "+i(e.value),1)],2)]))),128))])]),_:1},8,["visible","header"])])}}}),pt=Q(st,[["__scopeId","data-v-62b2d806"]]);export{pt as default};
