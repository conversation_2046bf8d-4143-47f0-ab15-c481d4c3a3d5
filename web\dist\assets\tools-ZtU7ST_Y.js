import{d as f,h as a,ab as O,ac as y,ad as d}from"./index-CTFw1yDv.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){d(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var C={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M6.53385 1.86579C9.60307 0.617451 13.2574 1.23778 15.7501 3.73054C17.9027 5.88313 18.6574 8.90017 18.0214 11.6586L23.1748 16.812L21.7605 18.2262L15.773 12.2387L15.9505 11.663C16.6384 9.43131 16.0992 6.908 14.3359 5.14475C12.7092 3.51804 10.4337 2.93229 8.34011 3.39158L12.5682 7.61962L7.6184 12.5694L3.39036 8.34133C2.93107 10.4349 3.51682 12.7104 5.14353 14.3371C6.90678 16.1004 9.43009 16.6396 11.6617 15.9517L12.2375 15.7742L18.225 21.7618L16.8108 23.176L11.6574 18.0226C8.89895 18.6587 5.88191 17.9039 3.72932 15.7514C1.23656 13.2586 0.61623 9.60429 1.86457 6.53507L2.11838 5.91103L3.79047 5.91301L7.6184 9.74094L9.73972 7.61962L5.91179 3.79169L5.90981 2.1196L6.53385 1.86579ZM15.7501 14.3371L20.6999 19.2869L19.2857 20.7011L14.3359 15.7514L15.7501 14.3371Z"}}]},b=f({name:"ToolsIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:o,style:c}=O(r),p=a(()=>["t-icon","t-icon-tools",o.value]),u=a(()=>s(s({},c.value),t.style)),v=a(()=>({class:p.value,style:u.value,onClick:L=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:L})}}));return()=>y(C,v.value)}});export{b as default};
