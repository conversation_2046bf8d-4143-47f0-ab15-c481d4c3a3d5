import{d as L,h as a,ab as O,ac as y,ad as d}from"./index-R826otwI.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function c(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){d(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var C={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M15.0966 5.90381C12.5582 3.3654 8.44267 3.3654 5.90426 5.90381C4.634 7.17406 3.99959 8.83721 4.00045 10.5034L4.00097 11.5034L2.00097 11.5044L2.00045 10.5044C1.99933 8.32916 2.82939 6.15025 4.49005 4.48959C7.8095 1.17014 13.1914 1.17014 16.5109 4.48959C19.5911 7.56983 19.8131 12.4259 17.1768 15.7621L22.5213 21.1066L21.1071 22.5208L15.7626 17.1763C14.2238 18.392 12.3609 19 10.5005 19H9.50045V17H10.5005C12.1655 17 13.8272 16.3656 15.0966 15.0962C17.6351 12.5578 17.6351 8.44221 15.0966 5.90381ZM1.67203 13.2574L4.50045 16.0858L7.32888 13.2574L8.74309 14.6716L5.91467 17.5L8.74309 20.3284L7.32888 21.7426L4.50045 18.9142L1.67203 21.7426L0.257812 20.3284L3.08624 17.5L0.257812 14.6716L1.67203 13.2574Z"}}]},m=L({name:"SearchErrorIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:o,style:s}=O(t),p=a(()=>["t-icon","t-icon-search-error",o.value]),u=a(()=>c(c({},s.value),r.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:f})}}));return()=>y(C,v.value)}});export{m as default};
