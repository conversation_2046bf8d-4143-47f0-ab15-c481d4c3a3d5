import{d,h as a,ab as v,ac as y,ad as O}from"./index-R826otwI.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){O(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M23 2H1V18H11.5176C11.5059 17.8348 11.5 17.6681 11.5 17.5 11.5 16.2242 11.8413 15.0281 12.4376 13.998H3V3.99805H21V10.9596C21.734 11.2404 22.4087 11.6411 23 12.1379V2zM3 20H11.9596C12.2404 20.734 12.6411 21.4087 13.1379 22H3V20z"}},{tag:"path",attrs:{fill:"currentColor",d:"M18.5 12C15.4624 12 13 14.4624 13 17.5C13 19.0184 13.6167 20.3948 14.6108 21.389C15.605 22.3832 16.9814 23 18.5 23C21.5376 23 24 20.5376 24 17.5C24 15.9816 23.3833 14.6052 22.3892 13.611C21.395 12.6168 20.0186 12 18.5 12ZM15 17.5C15 15.567 16.567 14 18.5 14C19.1032 14 19.6701 14.1521 20.1654 14.4206L15.4205 19.1652C15.152 18.67 15 18.1031 15 17.5ZM16.8346 20.5794L21.5795 15.8348C21.848 16.33 22 16.8969 22 17.5C22 19.433 20.433 21 18.5 21C17.8968 21 17.3299 20.8479 16.8346 20.5794Z"}}]},g=d({name:"SystemBlockedFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:l,style:c}=v(r),p=a(()=>["t-icon","t-icon-system-blocked-filled",l.value]),u=a(()=>s(s({},c.value),t.style)),f=a(()=>({class:p.value,style:u.value,onClick:C=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:C})}}));return()=>y(m,f.value)}});export{g as default};
