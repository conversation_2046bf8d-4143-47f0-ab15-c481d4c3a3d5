import{d as C,h as a,ab as L,ac as O,ad as y}from"./index-R826otwI.js";function l(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?l(Object(r),!0).forEach(function(t){y(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var d={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M1 20.0001H9.30278L12 18.2019L14.6972 20.0001H23V18.0001H15.3028L12 15.7982L8.69722 18.0001H1V20.0001ZM20.4472 16.3417L20.8944 15.4473C21.4388 14.3586 21.7509 13.3323 21.8135 12.2988L21.917 10.5877L20.3765 11.3398C19.9781 11.5344 19.5545 11.694 19.1085 11.8135C15.3742 12.8141 11.5359 10.598 10.5353 6.86378C10.1958 5.59695 10.1658 4.37554 10.4657 3.25967L10.9158 1.58545L9.24118 2.03415C3.90652 3.46357 0.74069 8.94694 2.17011 14.2816C2.22029 14.4689 2.33366 14.7676 2.41062 14.9662C2.45367 15.0772 2.49435 15.1797 2.5242 15.2542L2.56014 15.3435L2.57026 15.3684L2.95196 16.3038L4.8037 15.5481L4.42302 14.6153L4.41404 14.5931L4.3808 14.5105C4.35282 14.4407 4.31501 14.3454 4.2754 14.2433C4.18705 14.0153 4.11843 13.8254 4.10196 13.764C3.09915 10.0214 4.92342 6.18738 8.2657 4.52924C8.22897 5.47658 8.35 6.43563 8.60342 7.38142C9.87362 12.1219 14.7007 14.9595 19.4439 13.7922C19.3505 14.0346 19.2383 14.2874 19.1056 14.5529L18.6584 15.4473L20.4472 16.3417Z"}}]},m=C({name:"MoonRisingIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:o,style:c}=L(t),p=a(()=>["t-icon","t-icon-moon-rising",o.value]),u=a(()=>s(s({},c.value),r.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:f})}}));return()=>O(d,v.value)}});export{m as default};
