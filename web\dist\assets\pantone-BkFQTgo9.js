import{d as O,h as a,ab as y,ac as d,ad as L}from"./index-CTFw1yDv.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){L(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 25",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M14.3472 0.991211L17.525 5.44644L19.6238 4.73108L21.5385 10.3333H23V22H1L1 10.3333H2.00984L2.65212 10.3333L14.3472 0.991211ZM3.35284 12.3333L3.3496 12.3359H3L3 20H21V12.3333L3.35284 12.3333ZM19.425 10.3333L18.3878 7.29868L16.7836 7.81212L9.38658 10.3333H19.425ZM15.5488 6.12L13.9454 3.87198L7.84328 8.74633L15.5488 6.12ZM5.28516 15.164H7.28906V17.1679H5.28516V15.164Z"}}]},g=O({name:"PantoneIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:o,style:c}=y(r),p=a(()=>["t-icon","t-icon-pantone",o.value]),u=a(()=>s(s({},c.value),t.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:f})}}));return()=>d(m,v.value)}});export{g as default};
