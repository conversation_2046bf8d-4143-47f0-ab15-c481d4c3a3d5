import{d as V,h as a,ab as H,ac as O,ad as y}from"./index-CTFw1yDv.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function c(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){y(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var d={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M13 2V3.12602C14.7252 3.57006 16 5.13616 16 7H19V9H18V10H22V12H21V20H22V22H2V20H3V12H2V10H6V9H5V7H8C8 5.13616 9.27477 3.57006 11 3.12602V2H13ZM8 9V10H16V9H8ZM14 7C14 5.89543 13.1046 5 12 5C10.8954 5 10 5.89543 10 7H14ZM5 12V20H8V18C8 15.7909 9.79086 14 12 14C14.2091 14 16 15.7909 16 18V20H19V12H5ZM14 20V18C14 16.8954 13.1046 16 12 16C10.8954 16 10 16.8954 10 18V20H14Z"}}]},m=V({name:"PalaceIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:s}=H(t),p=a(()=>["t-icon","t-icon-palace",l.value]),u=a(()=>c(c({},s.value),r.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:f})}}));return()=>O(d,v.value)}});export{m as default};
