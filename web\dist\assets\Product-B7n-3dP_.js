import{d,_ as p,f as r,c as _,o as m,a as e,g as a,t as o,n as c,p as n}from"./index-R826otwI.js";const u=d({props:{data:{type:Object,default:()=>({})}}}),f={class:"operator-block operator-gap"},g={class:"operator-content"},h={class:"operator-title"},v={class:"operator-title-subtitle"},k={class:"operator-title-tags"},b={class:"operator-item"},z={class:"operator-item-info"},C={class:"operator-footer"},D={class:"operator-footer-percentage"};function y(t,$,B,E,N,V){const i=r("t-icon"),s=r("t-tag"),l=r("t-progress");return m(),_("div",f,[e("div",g,[e("div",h,[a(i,{name:"cart",class:"operator-title-icon"}),e("h1",null,o(t.data.name),1),e("div",v,o(t.data.subtitle),1),e("div",k,[a(s,{class:"operator-title-tag",theme:"success",size:"medium"},{default:c(()=>[n(o(t.data.size),1)]),_:1}),a(s,{class:"operator-title-tag",size:"medium"},{default:c(()=>[n(o(t.data.cpu),1)]),_:1}),a(s,{class:"operator-title-tag",size:"medium"},{default:c(()=>[n(o(t.data.memory),1)]),_:1})])]),e("div",b,[e("span",z,o(t.data.info),1),a(i,{class:"operator-item-icon",name:"chevron-right",size:"small",style:{color:"rgb(0 0 0 / 26%)"}})])]),e("div",C,[e("span",D,o(t.data.use)+" / "+o(t.data.stock)+"（台）",1),a(l,{class:"operator-progress",theme:"line",percentage:t.data.use/t.data.stock*100,label:!1,color:t.data.use/t.data.stock<.5?"#E24D59":"","track-color":t.data.use/t.data.stock<.5?"#FCD4D4":"#D4E3FC"},null,8,["percentage","color","track-color"])])])}const j=p(u,[["render",y],["__scopeId","data-v-9a7f6c2c"]]);export{j as default};
