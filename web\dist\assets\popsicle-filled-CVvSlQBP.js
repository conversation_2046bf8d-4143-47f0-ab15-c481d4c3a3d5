import{d,h as a,ab as O,ac as y,ad as m}from"./index-R826otwI.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){m(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var b={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M11.8349 2.34941C14.5801.2338 18.5351.434046 21.0512 2.95015 23.3963 5.2953 23.7296 8.89044 22.0511 11.5909 21.9981 11.4182 21.9365 11.2417 21.8649 11.063 21.1694 9.32861 19.582 7.5312 16.278 6.92007 13.664 6.43657 12.6301 5.11266 12.1837 3.99943 11.9485 3.41294 11.8634 2.84701 11.8387 2.424 11.8372 2.39855 11.836 2.37367 11.8349 2.34941zM10.0837 4.01809L2.31284 11.789 5.49378 14.9699 1.95782 18.5059C.981508 19.4822.981508 21.0651 1.95782 22.0414 2.93413 23.0177 4.51704 23.0177 5.49335 22.0414L9.02932 18.5055 12.2123 21.6885 20.3602 13.5406C20.3589 13.491 20.3568 13.4383 20.3535 13.3828 20.3288 12.9598 20.2437 12.3938 20.0085 11.8074 19.5622 10.6941 18.5282 9.37021 15.9142 8.88671 12.6102 8.27559 11.0228 6.47818 10.3274 4.74375 10.2281 4.49626 10.1481 4.25296 10.0837 4.01809z"}}]},P=d({name:"PopsicleFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:c}=O(t),p=a(()=>["t-icon","t-icon-popsicle-filled",l.value]),u=a(()=>s(s({},c.value),r.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>y(b,f.value)}});export{P as default};
