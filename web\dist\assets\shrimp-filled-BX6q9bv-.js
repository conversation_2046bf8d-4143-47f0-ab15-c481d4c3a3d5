import{d,h as a,ab as O,ac as m,ad as y}from"./index-R826otwI.js";function o(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?o(Object(r),!0).forEach(function(t){y(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var h={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M12.5003 21L12.5003 15H9C6.79086 15 5 16.7909 5 19V21H12.5003zM12.5003 3H1V4.375C1 6.65782 2.07377 8.47896 3.75676 9.69728 5.41388 10.8969 7.63387 11.5 10 11.5H12.5003L12.5003 3zM8.00195 4.99805V7.00195H5.99805V4.99805H8.00195zM14.5003 14.4152L14.5003 20.9861C16.2995 20.8859 17.9676 20.2498 19.332 19.2496L14.5003 14.4152zM22.9456 13C22.7415 14.8643 21.9725 16.5529 20.8054 17.8946L15.9136 13 22.9456 13zM20.8044 6.11394C21.9607 7.45451 22.737 9.14442 22.9444 11L15.9155 11 20.8044 6.11394zM14.5003 9.58679L19.3382 4.75167C17.9682 3.73643 16.3088 3.11121 14.5003 3.01347L14.5003 9.58679z"}}]},b=d({name:"ShrimpFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:c}=O(t),p=a(()=>["t-icon","t-icon-shrimp-filled",l.value]),u=a(()=>s(s({},c.value),r.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:v})}}));return()=>m(h,f.value)}});export{b as default};
