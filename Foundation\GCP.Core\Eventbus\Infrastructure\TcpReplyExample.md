# TCP消息自动回复功能

## 功能说明

当TCP客户端发送消息触发函数执行时，系统会自动将函数执行结果回复给发送方客户端。

## 工作原理

1. **消息接收**：TcpMessageBus接收到客户端消息时，会在消息头中自动添加客户端信息
2. **函数执行**：MessageConsumer执行函数并获取结果
3. **自动回复**：根据执行结果自动构建响应消息并发送给原客户端

## 消息流程

### 客户端发送请求

```json
{
  "messageId": "req_12345",
  "payload": {
    "userId": "user123",
    "action": "getUserInfo"
  },
  "headers": {
    "Topic": "user.query",
    "RequestId": "req_001"
  }
}
```

### 系统自动添加客户端信息

```json
{
  "messageId": "req_12345",
  "payload": {
    "userId": "user123", 
    "action": "getUserInfo"
  },
  "headers": {
    "Topic": "user.query",
    "RequestId": "req_001",
    "ClientId": "tcp_client_001",
    "Client": "[TcpSessionClient对象]"
  }
}
```

### 函数执行成功 - 自动回复

```json
{
  "messageId": "auto_generated_id",
  "payload": {
    "Success": true,
    "Message": "函数执行成功",
    "Data": {
      "userId": "user123",
      "userName": "张三",
      "email": "<EMAIL>"
    },
    "ProcessingTimeMs": 150.5,
    "Timestamp": "2024-01-01T12:00:00Z"
  },
  "headers": {
    "Topic": "user.query.response",
    "OriginalMessageId": "req_12345",
    "OriginalTopic": "user.query",
    "RequestId": "req_001",
    "ProcessingTimeMs": 150.5,
    "ReplyTime": "2024-01-01T12:00:00Z"
  }
}
```

### 函数执行失败 - 自动回复错误

```json
{
  "messageId": "auto_generated_id",
  "payload": {
    "Success": false,
    "Message": "函数执行失败: 用户不存在",
    "ErrorType": "UserNotFoundException",
    "ProcessingTimeMs": 50.2,
    "Timestamp": "2024-01-01T12:00:00Z"
  },
  "headers": {
    "Topic": "user.query.response",
    "OriginalMessageId": "req_12345",
    "OriginalTopic": "user.query",
    "RequestId": "req_001",
    "ProcessingTimeMs": 50.2,
    "ReplyTime": "2024-01-01T12:00:00Z",
    "Error": true
  }
}
```

## 响应主题规则

- **成功响应主题**：`{原主题}.response`
- **错误响应主题**：`{原主题}.response`
- **默认主题**：`tcp.function.response`（当原消息没有主题时）

## 客户端接收响应

客户端需要订阅响应主题来接收结果：

```javascript
// 订阅响应主题
tcpClient.subscribe('user.query.response', (response) => {
  if (response.payload.Success) {
    console.log('函数执行成功:', response.payload.Data);
    console.log('处理耗时:', response.payload.ProcessingTimeMs, 'ms');
  } else {
    console.error('函数执行失败:', response.payload.Message);
    console.error('错误类型:', response.payload.ErrorType);
  }
  
  // 通过RequestId关联原始请求
  console.log('原始请求ID:', response.headers.RequestId);
});

// 发送请求
tcpClient.send({
  messageId: generateId(),
  payload: { userId: 'user123', action: 'getUserInfo' },
  headers: { 
    Topic: 'user.query',
    RequestId: 'req_001'
  }
});
```

## 配置说明

### TcpMessageBus配置

```json
{
  "Port": "7789",
  "EventName": "TcpAutoReplyEvent"
}
```

### MessageConsumer配置

无需额外配置，系统会自动检测TCP消息并回复结果。

## 特性

### 1. 自动检测TCP消息
- 通过消息头中的`Client`字段判断是否为TCP消息
- 只对TCP消息进行自动回复，其他消息类型不受影响

### 2. 完整的错误处理
- 捕获函数执行异常并回复错误信息
- 包含错误类型和详细消息
- 不影响原有的异常处理流程

### 3. 性能监控
- 自动记录函数执行耗时
- 在响应中包含处理时间信息

### 4. 请求追踪
- 保留原始消息ID和主题
- 支持RequestId进行请求关联
- 完整的消息追踪链路

## 日志记录

系统会记录以下日志：

```
[DEBUG] TCP函数执行结果已回复: ClientId=tcp_client_001, Topic=user.query.response, ProcessingTime=150ms
[DEBUG] TCP响应发送成功: ClientId=tcp_client_001, Topic=user.query.response
[ERROR] TCP函数执行错误已回复: ClientId=tcp_client_001, Topic=user.query.response, Error=用户不存在
```

## 注意事项

1. **客户端订阅**：客户端必须订阅对应的响应主题才能接收回复
2. **消息格式**：响应消息遵循统一的成功/失败格式
3. **异常安全**：回复过程中的异常不会影响原有的消息处理流程
4. **性能影响**：自动回复功能对性能影响很小，主要是序列化和网络发送的开销

## 兼容性

- 只对TCP消息生效，不影响其他消息总线（如Kafka、RabbitMQ等）
- 向后兼容，不会影响现有的手动回复逻辑
- 可以与现有的消息处理流程无缝集成
