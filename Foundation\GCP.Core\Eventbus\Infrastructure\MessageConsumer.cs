using GCP.Common;
using GCP.DataAccess;
using GCP.FunctionPool;
using GCP.Iot.Models;
using Microsoft.Extensions.DependencyInjection;
using Serilog;
using System.Diagnostics;

namespace GCP.Eventbus.Infrastructure
{
    class MessageConsumer : IMessageConsumer
    {
        private readonly IMessageBus _messageBus;
        private readonly CancellationTokenSource _cts;
        private bool _isRunning;
        private bool _isDisposed;

        public string Name => Options.Name;
        public ConsumerOptions Options { get; }
        public bool IsRunning => _isRunning;

        public MessageConsumer(ConsumerOptions options, IMessageBus messageBus)
        {
            Options = options;
            _messageBus = messageBus;
            _cts = new CancellationTokenSource();
        }

        private static string ConsumerPipelineKeyPrefix => "$Consumer_";
        public async Task StartAsync(CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();

            if (_isRunning) return;

            try
            {
                var consumerPipelineKey = ConsumerPipelineKeyPrefix + Options.ConsumerId;
                ResiliencePipelineManager.TryAdd(consumerPipelineKey, () =>
                {
                    var handle = new ResilienceHandle<object>(consumerPipelineKey);

                    if (Options.RetryOptions != null)
                    {
                        handle.RetryCount = Options.RetryOptions.MaxRetries;
                        handle.RetryDelaysInSeconds = Options.RetryOptions.DelayMilliseconds / 1000;
                    }

                    return handle.Build((ex) =>
                    {
                        Log.Error(ex, "消息处理失败: {ConsumerId} {Name}, {Message}",
                            Options.ConsumerId, Name, ex.Message);
                        return Task.CompletedTask;
                    });
                });

                await _messageBus.SubscribeAsync(
                    Options.Topic,
                    async (message, ct) =>
                    {
                        try
                        {
                            using var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(ct, _cts.Token);
                            if (this.Options.IsFlow)
                            {
                                await ProcessMessageAsync(message, linkedCts.Token).ConfigureAwait(false);
                            }
                            else
                            {
                                if(this.Options.ConsumerFunc == null)
                                {
                                    throw new ArgumentException("ConsumerAction不能为空");
                                }
                                await this.Options.ConsumerFunc(message, linkedCts.Token).ConfigureAwait(false);
                            }
                        }
                        catch (OperationCanceledException) when (ct.IsCancellationRequested || _cts.Token.IsCancellationRequested)
                        {
                            // 正常地取消操作
                            throw;
                        }
                        catch (Exception ex)
                        {
                            Log.Error(ex, "Error processing message in consumer {Name}", Name);
                            throw;
                        }
                    },
                    Options,
                    cancellationToken).ConfigureAwait(false);

                _isRunning = true;
                //Log.Information("Started consumer: {Name}", Name);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Failed to start consumer: {Name}", Name);
                throw;
            }
        }

        private async Task ProcessMessageAsync(MessageEnvelope message, CancellationToken cancellationToken)
        {
            var solutionId = Options.Settings.GetValueOrDefault("SolutionId", "*");
            var projectId = Options.Settings.GetValueOrDefault("ProjectId", "*");

            if (message == null)
            {
                throw new ArgumentException("消息为空");
            }

            await ResiliencePipelineManager.ExecuteAsync(ConsumerPipelineKeyPrefix + Options.ConsumerId, async (token) =>
            {
                var procLog = new LcFruProc
                {
                    Id = TUID.NewTUID().ToString(),
                    TimeCreate = DateTime.Now,
                    Creator = "sys",
                    SolutionId = solutionId,
                    ProjectId = projectId,
                    FunctionId = Options.ConsumerId,
                    FunctionName = Options.Topic,
                    Version = 1,
                    TriggerType = "EVENT",
                    Status = 0,
                    BeginTime = DateTime.Now,
                    TotalTraffic = 0,
                };

                var context = new FunctionContext();
                context.LocalDbContext.Value = context.scope.ServiceProvider.GetRequiredService<IDbContext>();

                var sw = new Stopwatch();

                try
                {
                    IDictionary<string, object> args;
                    if (message.Payload is string str)
                    {
                        // 第三方发送的文本消息，记录流量
                        procLog.TotalTraffic = str.Length / 1024;
                        args = JsonHelper.Deserialize<Dictionary<string, object>>(str);
                    }
                    else if (message.Payload is IDictionary<string, object> dict)
                    {
                        args = dict;
                    }
                    else if (message.Payload is EquipmentDataChangedEventArgs dataChangedArgs)
                    {
                        args = new Dictionary<string, object>
                        {
                            { "EquipmentId", dataChangedArgs.EquipmentId },
                            { "EquipmentType", dataChangedArgs.EquipmentType },
                            { "Timestamp", dataChangedArgs.Timestamp },
                            { "Values", dataChangedArgs.Values },
                        };
                    }
                    else
                    {
                        throw new ArgumentException($"不支持的消息类型: {message.Payload.GetType()}");
                    }

                    var result = await FunctionHelper.Runner.Execute(new FunctionInvokeDTO
                    {
                        path = Options.ConsumerId,
                        args = args
                    }, (ctx) =>
                    {
                        ctx.Persistence = SqlLogSettings.MessageStatus.Step;
                        ctx.trackId = procLog.Id;
                        if (SqlLogSettings.MessageStatus.Step)
                            ctx.Middlewares.Add(TimeMiddleware.Handler);
                        context = ctx;

                        if (SqlLogSettings.MessageStatus.Flow)
                            _ = context.SqlLog.Write(procLog);
                        sw.Start();
                        return Task.CompletedTask;
                    }).ConfigureAwait(false);

                    // 如果是TCP消息，尝试回复结果
                    await TryReplyTcpResult(message, result, sw.ElapsedMilliseconds);

                    procLog.Status = 1;
                }
                catch (CustomSkipException)
                {
                    sw.Stop();
                    procLog.Status = -1;
                    throw;
                }
                catch (Exception ex)
                {
                    sw.Stop();
                    procLog.Status = -1;
                    if (context != null && !string.IsNullOrEmpty(context.trackId))
                    {
                        _ = context.SqlLog.Error(ex, "消息处理异常：" + ex.Message, true);
                    }
                    Log.Error(ex, "消息处理异常");

                    // 如果是TCP消息，回复错误结果
                    await TryReplyTcpError(message, ex, sw.ElapsedMilliseconds);

                    throw;
                }
                finally
                {
                    sw.Stop();
                    if (context != null && SqlLogSettings.MessageStatus.Flow)
                    {
                        procLog.EndTime = DateTime.Now;
                        procLog.Duration = (int)sw.ElapsedMilliseconds;
                        _ = context.SqlLog.Write(procLog, SqlType.Update);
                    }
                }
            }, cancellationToken: cancellationToken).ConfigureAwait(false);
        }

        public async Task StopAsync(CancellationToken cancellationToken = default)
        {
            if (!_isRunning) return;

            try
            {
                ResiliencePipelineManager.TryRemove(ConsumerPipelineKeyPrefix + Options.ConsumerId);
                await _cts.CancelAsync();
                await _messageBus.UnsubscribeAsync(Options.Topic, cancellationToken);
                _isRunning = false;
                Log.Information("Stopped consumer: {Name}", Name);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error stopping consumer: {Name}", Name);
                throw;
            }
        }

        /// <summary>
        /// 尝试回复TCP消息结果
        /// </summary>
        /// <param name="message">原始消息</param>
        /// <param name="result">函数执行结果</param>
        /// <param name="processingTimeMs">处理耗时（毫秒）</param>
        /// <returns></returns>
        private async Task TryReplyTcpResult(MessageEnvelope message, object result, long processingTimeMs)
        {
            try
            {
                // 检查是否是TCP消息（通过header中是否有Client信息判断）
                if (!message.Headers.TryGetValue("Client", out var clientObj) || clientObj == null)
                {
                    // 不是TCP消息或没有客户端信息，跳过回复
                    return;
                }

                // 检查是否是TcpSessionClient
                if (clientObj is not TouchSocket.Sockets.TcpSessionClient tcpClient)
                {
                    Log.Debug("客户端对象不是TcpSessionClient类型，跳过TCP回复");
                    return;
                }

                // 获取原始消息ID和主题
                var originalMessageId = message.Headers.TryGetValue("MessageId", out var msgId) ? msgId?.ToString() : null;
                var originalTopic = message.Headers.TryGetValue("Topic", out var topic) ? topic?.ToString() : null;
                var requestId = message.Headers.TryGetValue("RequestId", out var reqId) ? reqId?.ToString() : null;

                // 构建响应数据
                var responseData = new
                {
                    Success = true,
                    Message = "函数执行成功",
                    Data = result,
                    ProcessingTimeMs = processingTimeMs,
                    Timestamp = DateTime.UtcNow
                };

                // 构建响应消息头
                var responseHeaders = new Dictionary<string, object>
                {
                    ["OriginalMessageId"] = originalMessageId,
                    ["OriginalTopic"] = originalTopic,
                    ["ProcessingTimeMs"] = processingTimeMs,
                    ["ReplyTime"] = DateTime.UtcNow
                };

                if (!string.IsNullOrEmpty(requestId))
                {
                    responseHeaders["RequestId"] = requestId;
                }

                // 构建响应主题（在原主题后加.response）
                var responseTopic = !string.IsNullOrEmpty(originalTopic) ? $"{originalTopic}.response" : "tcp.function.response";

                // 发送响应
                await SendTcpResponse(tcpClient, responseTopic, responseData, responseHeaders);

                Log.Debug("TCP函数执行结果已回复: ClientId={ClientId}, Topic={Topic}, ProcessingTime={ProcessingTimeMs}ms",
                    tcpClient.Id, responseTopic, processingTimeMs);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "回复TCP消息结果时发生错误");
                // 不抛出异常，避免影响主流程
            }
        }

        /// <summary>
        /// 尝试回复TCP消息错误结果
        /// </summary>
        /// <param name="message">原始消息</param>
        /// <param name="exception">异常信息</param>
        /// <param name="processingTimeMs">处理耗时（毫秒）</param>
        /// <returns></returns>
        private async Task TryReplyTcpError(MessageEnvelope message, Exception exception, long processingTimeMs)
        {
            try
            {
                // 检查是否是TCP消息（通过header中是否有Client信息判断）
                if (!message.Headers.TryGetValue("Client", out var clientObj) || clientObj == null)
                {
                    // 不是TCP消息或没有客户端信息，跳过回复
                    return;
                }

                // 检查是否是TcpSessionClient
                if (clientObj is not TouchSocket.Sockets.TcpSessionClient tcpClient)
                {
                    Log.Debug("客户端对象不是TcpSessionClient类型，跳过TCP错误回复");
                    return;
                }

                // 获取原始消息ID和主题
                var originalMessageId = message.Headers.TryGetValue("MessageId", out var msgId) ? msgId?.ToString() : null;
                var originalTopic = message.Headers.TryGetValue("Topic", out var topic) ? topic?.ToString() : null;
                var requestId = message.Headers.TryGetValue("RequestId", out var reqId) ? reqId?.ToString() : null;

                // 构建错误响应数据
                var errorResponseData = new
                {
                    Success = false,
                    Message = $"函数执行失败: {exception.Message}",
                    ErrorType = exception.GetType().Name,
                    ProcessingTimeMs = processingTimeMs,
                    Timestamp = DateTime.UtcNow
                };

                // 构建响应消息头
                var responseHeaders = new Dictionary<string, object>
                {
                    ["OriginalMessageId"] = originalMessageId,
                    ["OriginalTopic"] = originalTopic,
                    ["ProcessingTimeMs"] = processingTimeMs,
                    ["ReplyTime"] = DateTime.UtcNow,
                    ["Error"] = true
                };

                if (!string.IsNullOrEmpty(requestId))
                {
                    responseHeaders["RequestId"] = requestId;
                }

                // 构建响应主题（在原主题后加.response）
                var responseTopic = !string.IsNullOrEmpty(originalTopic) ? $"{originalTopic}.response" : "tcp.function.response";

                // 发送错误响应
                await SendTcpResponse(tcpClient, responseTopic, errorResponseData, responseHeaders);

                Log.Debug("TCP函数执行错误已回复: ClientId={ClientId}, Topic={Topic}, Error={ErrorMessage}",
                    tcpClient.Id, responseTopic, exception.Message);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "回复TCP错误消息时发生异常");
                // 不抛出异常，避免影响主流程
            }
        }

        /// <summary>
        /// 发送TCP响应
        /// </summary>
        /// <param name="client">TCP客户端</param>
        /// <param name="topic">响应主题</param>
        /// <param name="responseData">响应数据</param>
        /// <param name="headers">响应头</param>
        /// <returns></returns>
        private async Task SendTcpResponse(TouchSocket.Sockets.TcpSessionClient client, string topic, object responseData, IDictionary<string, object> headers)
        {
            var envelope = new MessageEnvelope
            {
                Payload = responseData,
                Headers = headers ?? new Dictionary<string, object>()
            };
            envelope.Headers["Topic"] = topic;

            var json = JsonHelper.Serialize(envelope);

            try
            {
                var bytes = System.Text.Encoding.UTF8.GetBytes(json);
                await client.SendAsync(bytes);
                Log.Debug("TCP响应发送成功: ClientId={ClientId}, Topic={Topic}", client.Id, topic);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "发送TCP响应失败: ClientId={ClientId}, Topic={Topic}", client.Id, topic);
                throw;
            }
        }

        private void ThrowIfDisposed()
        {
            if (_isDisposed)
            {
                throw new ObjectDisposedException(GetType().Name);
            }
        }

        public async ValueTask DisposeAsync()
        {
            if (_isDisposed) return;

            try
            {
                if (_isRunning)
                {
                    await StopAsync();
                }
            }
            finally
            {
                _cts.Dispose();
                _isDisposed = true;
            }
        }
    }
}