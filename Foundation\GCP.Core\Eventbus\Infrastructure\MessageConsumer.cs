using GCP.Common;
using GCP.DataAccess;
using GCP.FunctionPool;
using GCP.Iot.Models;
using Microsoft.Extensions.DependencyInjection;
using Serilog;
using System.Diagnostics;

namespace GCP.Eventbus.Infrastructure
{
    class MessageConsumer : IMessageConsumer
    {
        private readonly IMessageBus _messageBus;
        private readonly CancellationTokenSource _cts;
        private bool _isRunning;
        private bool _isDisposed;

        public string Name => Options.Name;
        public ConsumerOptions Options { get; }
        public bool IsRunning => _isRunning;

        public MessageConsumer(ConsumerOptions options, IMessageBus messageBus)
        {
            Options = options;
            _messageBus = messageBus;
            _cts = new CancellationTokenSource();
        }

        private static string ConsumerPipelineKeyPrefix => "$Consumer_";
        public async Task StartAsync(CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();

            if (_isRunning) return;

            try
            {
                var consumerPipelineKey = ConsumerPipelineKeyPrefix + Options.ConsumerId;
                ResiliencePipelineManager.TryAdd(consumerPipelineKey, () =>
                {
                    var handle = new ResilienceHandle<object>(consumerPipelineKey);

                    if (Options.RetryOptions != null)
                    {
                        handle.RetryCount = Options.RetryOptions.MaxRetries;
                        handle.RetryDelaysInSeconds = Options.RetryOptions.DelayMilliseconds / 1000;
                    }

                    return handle.Build((ex) =>
                    {
                        Log.Error(ex, "消息处理失败: {ConsumerId} {Name}, {Message}",
                            Options.ConsumerId, Name, ex.Message);
                        return Task.CompletedTask;
                    });
                });

                await _messageBus.SubscribeAsync(
                    Options.Topic,
                    async (message, ct) =>
                    {
                        try
                        {
                            using var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(ct, _cts.Token);
                            if (this.Options.IsFlow)
                            {
                                await ProcessMessageAsync(message, linkedCts.Token).ConfigureAwait(false);
                            }
                            else
                            {
                                if(this.Options.ConsumerFunc == null)
                                {
                                    throw new ArgumentException("ConsumerAction不能为空");
                                }
                                await this.Options.ConsumerFunc(message, linkedCts.Token).ConfigureAwait(false);
                            }
                        }
                        catch (OperationCanceledException) when (ct.IsCancellationRequested || _cts.Token.IsCancellationRequested)
                        {
                            // 正常地取消操作
                            throw;
                        }
                        catch (Exception ex)
                        {
                            Log.Error(ex, "Error processing message in consumer {Name}", Name);
                            throw;
                        }
                    },
                    Options,
                    cancellationToken).ConfigureAwait(false);

                _isRunning = true;
                //Log.Information("Started consumer: {Name}", Name);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Failed to start consumer: {Name}", Name);
                throw;
            }
        }

        private async Task ProcessMessageAsync(MessageEnvelope message, CancellationToken cancellationToken)
        {
            var solutionId = Options.Settings.GetValueOrDefault("SolutionId", "*");
            var projectId = Options.Settings.GetValueOrDefault("ProjectId", "*");

            if (message == null)
            {
                throw new ArgumentException("消息为空");
            }

            await ResiliencePipelineManager.ExecuteAsync(ConsumerPipelineKeyPrefix + Options.ConsumerId, async (token) =>
            {
                var procLog = new LcFruProc
                {
                    Id = TUID.NewTUID().ToString(),
                    TimeCreate = DateTime.Now,
                    Creator = "sys",
                    SolutionId = solutionId,
                    ProjectId = projectId,
                    FunctionId = Options.ConsumerId,
                    FunctionName = Options.Topic,
                    Version = 1,
                    TriggerType = "EVENT",
                    Status = 0,
                    BeginTime = DateTime.Now,
                    TotalTraffic = 0,
                };

                var context = new FunctionContext();
                context.LocalDbContext.Value = context.scope.ServiceProvider.GetRequiredService<IDbContext>();

                var sw = new Stopwatch();

                try
                {
                    IDictionary<string, object> args;
                    if (message.Payload is string str)
                    {
                        // 第三方发送的文本消息，记录流量
                        procLog.TotalTraffic = str.Length / 1024;
                        args = JsonHelper.Deserialize<Dictionary<string, object>>(str);
                    }
                    else if (message.Payload is IDictionary<string, object> dict)
                    {
                        args = dict;
                    }
                    else if (message.Payload is EquipmentDataChangedEventArgs dataChangedArgs)
                    {
                        args = new Dictionary<string, object>
                        {
                            { "EquipmentId", dataChangedArgs.EquipmentId },
                            { "EquipmentType", dataChangedArgs.EquipmentType },
                            { "Timestamp", dataChangedArgs.Timestamp },
                            { "Values", dataChangedArgs.Values },
                        };
                    }
                    else
                    {
                        throw new ArgumentException($"不支持的消息类型: {message.Payload.GetType()}");
                    }

                    await FunctionHelper.Runner.Execute(new FunctionInvokeDTO
                    {
                        path = Options.ConsumerId,
                        args = args
                    }, (ctx) =>
                    {
                        ctx.Persistence = SqlLogSettings.MessageStatus.Step;
                        ctx.trackId = procLog.Id;
                        if (SqlLogSettings.MessageStatus.Step)
                            ctx.Middlewares.Add(TimeMiddleware.Handler);
                        context = ctx;

                        if (SqlLogSettings.MessageStatus.Flow)
                            _ = context.SqlLog.Write(procLog);
                        sw.Start();
                        return Task.CompletedTask;
                    }).ConfigureAwait(false);

                    procLog.Status = 1;
                }
                catch (CustomSkipException)
                {
                    sw.Stop();
                    procLog.Status = -1;
                    throw;
                }
                catch (Exception ex)
                {
                    sw.Stop();
                    procLog.Status = -1;
                    if (context != null && !string.IsNullOrEmpty(context.trackId))
                    {
                        _ = context.SqlLog.Error(ex, "消息处理异常：" + ex.Message, true);
                    }
                    Log.Error(ex, "消息处理异常");
                    throw;
                }
                finally
                {
                    sw.Stop();
                    if (context != null && SqlLogSettings.MessageStatus.Flow)
                    {
                        procLog.EndTime = DateTime.Now;
                        procLog.Duration = (int)sw.ElapsedMilliseconds;
                        _ = context.SqlLog.Write(procLog, SqlType.Update);
                    }
                }
            }, cancellationToken: cancellationToken).ConfigureAwait(false);
        }

        public async Task StopAsync(CancellationToken cancellationToken = default)
        {
            if (!_isRunning) return;

            try
            {
                ResiliencePipelineManager.TryRemove(ConsumerPipelineKeyPrefix + Options.ConsumerId);
                await _cts.CancelAsync();
                await _messageBus.UnsubscribeAsync(Options.Topic, cancellationToken);
                _isRunning = false;
                Log.Information("Stopped consumer: {Name}", Name);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error stopping consumer: {Name}", Name);
                throw;
            }
        }

        private void ThrowIfDisposed()
        {
            if (_isDisposed)
            {
                throw new ObjectDisposedException(GetType().Name);
            }
        }

        public async ValueTask DisposeAsync()
        {
            if (_isDisposed) return;

            try
            {
                if (_isRunning)
                {
                    await StopAsync();
                }
            }
            finally
            {
                _cts.Dispose();
                _isDisposed = true;
            }
        }
    }
}