import{d as Y,ag as N,r as x,h as k,i as L,Y as w,ah as O,K as m,k as P,ai as K,f as p,b as j,o as q,n as i,g as d,a as C,a2 as M,u as G,_ as H}from"./index-R826otwI.js";import{L as J}from"./date-DdgLqdlw.js";import{g as $,a as Q}from"./index-JBp7_miI.js";import{u as R,i as U,a as X,b as y}from"./installCanvasRenderer-akfsAsjg.js";import{i as Z,a as V,b as tt}from"./install-DYqkEeHz.js";import{i as et}from"./install-UPPBFq-o.js";import"./dayjs.min-Dvo2iNbV.js";import"./charts-PDyqFShm.js";import"./sectorHelper-Bt-evzC6.js";const at={class:"dashboard-chart-title-container"},nt=Y({__name:"MiddleChart",setup(ot){R([Z,V,et,tt,U,X]);const b=t=>{let e;if(!t||t.length===0)return e=new Date,`${e.getFullYear()}-${e.getMonth()+1}`;e=new Date(t[0]);const s=new Date(t[1]),h=e.getMonth()+1>9?e.getMonth()+1:`0${e.getMonth()+1}`,u=s.getMonth()+1>9?s.getMonth()+1:`0${s.getMonth()+1}`;return`${e.getFullYear()}-${h}  至  ${s.getFullYear()}-${u}`},r=N(),a=x(1),g=k(()=>r.chartColors);let l,o;const S=()=>{l||(l=document.getElementById("monitorContainer")),o=y(l),o.setOption($({...g.value}))};let _,n;const T=()=>{_||(_=document.getElementById("countContainer")),n=y(_),n.setOption(Q(g.value)),n.dispatchAction({type:"downplay",seriesIndex:0,dataIndex:-1}),n.dispatchAction({type:"highlight",seriesIndex:0,dataIndex:1}),n.dispatchAction({type:"showTip",seriesIndex:0,dataIndex:1})},v=()=>{S(),T()},c=()=>{document.documentElement.clientWidth>=1400&&document.documentElement.clientWidth<1920?a.value=Number((document.documentElement.clientWidth/2080).toFixed(2)):document.documentElement.clientWidth<1080?a.value=Number((document.documentElement.clientWidth/1080).toFixed(2)):a.value=1,o.resize({width:l.clientWidth,height:a.value*326}),n.resize({width:a.value*326,height:a.value*326})};L(()=>{v(),w(()=>{c()})});const{width:W,height:I}=O();m([W,I],()=>{c()}),P(()=>{D(),B(),E()});const f=x(b()),B=m(()=>r.brandTheme,()=>{K([o,n])}),E=m(()=>r.isSidebarCompact,()=>{r.isSidebarCompact?w(()=>{c()}):setTimeout(()=>{c()},180)}),D=m(()=>r.mode,()=>{[o,n].forEach(t=>{t.dispose()}),v()}),z=t=>{f.value=b(t),o.setOption($({dateTime:t,...g.value}))};return(t,e)=>{const s=p("t-date-range-picker"),h=p("t-card"),u=p("t-col"),A=p("t-row");return q(),j(A,{gutter:16,class:"row-container"},{default:i(()=>[d(u,{xs:12,xl:9},{default:i(()=>[d(h,{title:t.t("pages.dashboardBase.topPanel.analysis.title"),subtitle:f.value,class:"dashboard-chart-card",bordered:!1},{actions:i(()=>[C("div",at,[d(s,{class:"card-date-picker-container",theme:"primary",mode:"date","default-value":G(J),onChange:e[0]||(e[0]=F=>z(F))},null,8,["default-value"])])]),default:i(()=>[C("div",{id:"monitorContainer",class:"dashboard-chart-container",style:M({width:"100%",height:`${a.value*326}px`})},null,4)]),_:1},8,["title","subtitle"])]),_:1}),d(u,{xs:12,xl:3},{default:i(()=>[d(h,{title:t.t("pages.dashboardBase.topPanel.analysis.channels"),subtitle:f.value,class:"dashboard-chart-card",bordered:!1},{default:i(()=>[C("div",{id:"countContainer",class:"dashboard-chart-container",style:M({width:`${a.value*326}px`,height:`${a.value*326}px`,margin:"0 auto"})},null,4)]),_:1},8,["title","subtitle"])]),_:1})]),_:1})}}}),pt=H(nt,[["__scopeId","data-v-35bc3ab3"]]);export{pt as default};
