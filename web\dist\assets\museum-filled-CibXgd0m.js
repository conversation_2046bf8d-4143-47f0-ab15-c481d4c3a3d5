import{d,h as a,ab as m,ac as O,ad as y}from"./index-R826otwI.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){y(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var g={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M6 2H18V6.32718L22 5.88274V14H17V22H2V8.10496L6 7.66051V2ZM8 7.43829L16 6.5494V4H8V7.43829ZM6 10.998V13.002H8.00391V10.998H6ZM10 10.998V13.002H12.0039V10.998H10Z"}},{tag:"path",attrs:{fill:"currentColor",d:"M19 22H22V16H19V22Z"}}]},b=d({name:"MuseumFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:l,style:c}=m(r),u=a(()=>["t-icon","t-icon-museum-filled",l.value]),p=a(()=>s(s({},c.value),t.style)),f=a(()=>({class:u.value,style:p.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>O(g,f.value)}});export{b as default};
