import{d as M,r as A,h as z,f as r,c as N,o as g,a as i,g as e,w as c,n as s,v as V,t as u,F as I,s as P,u as S,b as D,p as n,G,_ as Y}from"./index-CTFw1yDv.js";const q={name:[{required:!0,type:"error"}],type:[{required:!0,type:"error"}],title:[{required:!0,type:"error"}],taxNum:[{required:!0,type:"error"}],consignee:[{required:!0,type:"error"}],mobileNum:[{required:!0,type:"error"}],deliveryAddress:[{required:!0,type:"error"}],fullAddress:[{required:!0,type:"error"}]},j=[{label:"A",value:"1"},{label:"B",value:"2"},{label:"C",value:"3"}],H=[{label:"Type A",value:"1"},{label:"Type B",value:"2"},{label:"Type C",value:"3"}],J=[{label:"广东省深圳市南山区",value:"1"},{label:"北京市海淀区",value:"2"},{label:"上海市徐汇区",value:"3"},{label:"四川省成都市高新区",value:"4"},{label:"广东省广州市天河区",value:"5"},{label:"陕西省西安市高新区",value:"6"}],K={name:"",type:""},Q={title:"",taxNum:"",address:"",bank:"",bankAccount:"",email:"",tel:""},W={consignee:"",mobileNum:"",deliveryAddress:"",fullAddress:""},X={class:"form-step-container"},Z={class:"rule-tips"},x={class:"step-form-4"},ee={class:"text"},le={class:"tips"},te={class:"button-group"},ae={name:"FormStep"},se=M({...ae,setup(oe){const f=A({...K}),p=A({...Q}),d=A({...W}),b=A(0),R=z(()=>f.value.name==="1"?"565421":f.value.name==="2"?"278821":f.value.name==="3"?"109824":"--"),k=(l,a)=>{l.validateResult===!0&&(b.value=a)},h=l=>{b.value=l},C=()=>{G().replace({path:"/detail/advanced"})};return(l,a)=>{const _=r("t-step-item"),O=r("t-steps"),B=r("t-card"),E=r("t-alert"),T=r("t-option"),w=r("t-select"),o=r("t-form-item"),v=r("t-button"),U=r("t-form"),m=r("t-input"),F=r("t-textarea"),L=r("t-icon"),$=r("t-space");return g(),N("div",null,[i("div",X,[e(B,{bordered:!1},{default:s(()=>[e(O,{class:"step-container",current:1,status:"process"},{default:s(()=>[e(_,{title:l.t("pages.formStep.step1.title"),content:l.t("pages.formStep.step1.subtitle")},null,8,["title","content"]),e(_,{title:l.t("pages.formStep.step2.title"),content:l.t("pages.formStep.step2.subtitle")},null,8,["title","content"]),e(_,{title:l.t("pages.formStep.step3.title"),content:l.t("pages.formStep.step3.subtitle")},null,8,["title","content"]),e(_,{title:l.t("pages.formStep.step4.title"),content:l.t("pages.formStep.step4.subtitle")},null,8,["title","content"])]),_:1})]),_:1}),c(i("div",Z,[e(E,{theme:"info",title:l.t("pages.formStep.step1.rules"),close:!0},{message:s(()=>[i("p",null,u(l.t("pages.formStep.step1.rule1")),1),i("p",null,u(l.t("pages.formStep.step1.rule2")),1),i("p",null,u(l.t("pages.formStep.step1.rule3")),1)]),_:1},8,["title"])],512),[[V,b.value===0]]),c(e(U,{class:"step-form",data:f.value,rules:S(q),"label-align":"right",onSubmit:a[2]||(a[2]=t=>k(t,1))},{default:s(()=>[e(o,{label:l.t("pages.formStep.step1.contractName"),name:"name"},{default:s(()=>[e(w,{modelValue:f.value.name,"onUpdate:modelValue":a[0]||(a[0]=t=>f.value.name=t),style:{width:"480px"},class:"demo-select-base",clearable:""},{default:s(()=>[(g(!0),N(I,null,P(S(j),(t,y)=>(g(),D(T,{key:y,value:t.value,label:t.label},{default:s(()=>[n(u(t.label),1)]),_:2},1032,["value","label"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["label"]),e(o,{label:l.t("pages.formStep.step1.invoiceType"),name:"type"},{default:s(()=>[e(w,{modelValue:f.value.type,"onUpdate:modelValue":a[1]||(a[1]=t=>f.value.type=t),style:{width:"480px"},class:"demo-select-base",clearable:""},{default:s(()=>[(g(!0),N(I,null,P(S(H),(t,y)=>(g(),D(T,{key:y,value:t.value,label:t.label},{default:s(()=>[n(u(t.label),1)]),_:2},1032,["value","label"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["label"]),e(o,{label:l.t("pages.formStep.step1.amount")},{default:s(()=>[n(" ¥ "+u(R.value),1)]),_:1},8,["label"]),e(o,null,{default:s(()=>[e(v,{theme:"primary",type:"submit"},{default:s(()=>[n(u(l.t("pages.formStep.step1.submit")),1)]),_:1})]),_:1})]),_:1},8,["data","rules"]),[[V,b.value===0]]),c(e(U,{class:"step-form",data:p.value,rules:S(q),"label-align":"left",onReset:a[10]||(a[10]=t=>h(0)),onSubmit:a[11]||(a[11]=t=>k(t,2))},{default:s(()=>[e(o,{label:l.t("pages.formStep.step2.invoiceTitle"),name:"title"},{default:s(()=>[e(m,{modelValue:p.value.title,"onUpdate:modelValue":a[3]||(a[3]=t=>p.value.title=t),style:{width:"480px"},placeholder:l.t("pages.formStep.step2.invoiceTitlePlaceholder")},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),e(o,{label:l.t("pages.formStep.step2.taxNum"),name:"taxNum"},{default:s(()=>[e(m,{modelValue:p.value.taxNum,"onUpdate:modelValue":a[4]||(a[4]=t=>p.value.taxNum=t),style:{width:"480px"},placeholder:l.t("pages.formStep.step2.taxNumPlaceholder")},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),e(o,{label:l.t("pages.formStep.step2.address"),name:"address"},{default:s(()=>[e(m,{modelValue:p.value.address,"onUpdate:modelValue":a[5]||(a[5]=t=>p.value.address=t),style:{width:"480px"},placeholder:l.t("pages.formStep.step2.addressPlaceholder")},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),e(o,{label:l.t("pages.formStep.step2.bank"),name:"bank"},{default:s(()=>[e(m,{modelValue:p.value.bank,"onUpdate:modelValue":a[6]||(a[6]=t=>p.value.bank=t),style:{width:"480px"},placeholder:l.t("pages.formStep.step2.bankPlaceholder")},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),e(o,{label:l.t("pages.formStep.step2.bankAccount"),name:"bankAccount"},{default:s(()=>[e(m,{modelValue:p.value.bankAccount,"onUpdate:modelValue":a[7]||(a[7]=t=>p.value.bankAccount=t),style:{width:"480px"},placeholder:l.t("pages.formStep.step2.bankAccountPlaceholder")},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),e(o,{label:l.t("pages.formStep.step2.email"),name:"email"},{default:s(()=>[e(m,{modelValue:p.value.email,"onUpdate:modelValue":a[8]||(a[8]=t=>p.value.email=t),style:{width:"480px"},placeholder:l.t("pages.formStep.step2.emailPlaceholder")},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),e(o,{label:l.t("pages.formStep.step2.tel"),name:"tel"},{default:s(()=>[e(m,{modelValue:p.value.tel,"onUpdate:modelValue":a[9]||(a[9]=t=>p.value.tel=t),style:{width:"480px"},placeholder:l.t("pages.formStep.step2.telPlaceholder")},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),e(o,null,{default:s(()=>[e(v,{type:"reset",theme:"default",variant:"base"},{default:s(()=>[n(u(l.t("pages.formStep.preStep")),1)]),_:1}),e(v,{theme:"primary",type:"submit"},{default:s(()=>[n(u(l.t("pages.formStep.nextStep")),1)]),_:1})]),_:1})]),_:1},8,["data","rules"]),[[V,b.value===1]]),c(e(U,{class:"step-form",data:d.value,rules:S(q),"label-align":"left",onReset:a[16]||(a[16]=t=>h(1)),onSubmit:a[17]||(a[17]=t=>k(t,6))},{default:s(()=>[e(o,{label:l.t("pages.formStep.step3.consignee"),name:"consignee"},{default:s(()=>[e(m,{modelValue:d.value.consignee,"onUpdate:modelValue":a[12]||(a[12]=t=>d.value.consignee=t),style:{width:"480px"}},null,8,["modelValue"])]),_:1},8,["label"]),e(o,{label:l.t("pages.formStep.step3.mobileNum"),name:"mobileNum"},{default:s(()=>[e(m,{modelValue:d.value.mobileNum,"onUpdate:modelValue":a[13]||(a[13]=t=>d.value.mobileNum=t),style:{width:"480px"}},null,8,["modelValue"])]),_:1},8,["label"]),e(o,{label:l.t("pages.formStep.step3.deliveryAddress"),name:"deliveryAddress"},{default:s(()=>[e(w,{modelValue:d.value.deliveryAddress,"onUpdate:modelValue":a[14]||(a[14]=t=>d.value.deliveryAddress=t),style:{width:"480px"},class:"demo-select-base",clearable:""},{default:s(()=>[(g(!0),N(I,null,P(S(J),(t,y)=>(g(),D(T,{key:y,value:t.value,label:t.label},{default:s(()=>[n(u(t.label),1)]),_:2},1032,["value","label"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["label"]),e(o,{label:l.t("pages.formStep.step3.fullAddress"),name:"fullAddress"},{default:s(()=>[e(F,{modelValue:d.value.fullAddress,"onUpdate:modelValue":a[15]||(a[15]=t=>d.value.fullAddress=t),style:{width:"480px"}},null,8,["modelValue"])]),_:1},8,["label"]),e(o,null,{default:s(()=>[e(v,{type:"reset",theme:"default",variant:"base"},{default:s(()=>[n(u(l.t("pages.formStep.preStep")),1)]),_:1}),e(v,{theme:"primary",type:"submit"},{default:s(()=>[n(u(l.t("pages.formStep.nextStep")),1)]),_:1})]),_:1})]),_:1},8,["data","rules"]),[[V,b.value===2]]),c(i("div",x,[e($,{direction:"vertical",style:{"align-items":"center"}},{default:s(()=>[e(L,{name:"check-circle-filled",style:{color:"green"},size:"52px"}),i("p",ee,u(l.t("pages.formStep.step4.finishTitle")),1),i("p",le,u(l.t("pages.formStep.step4.finishTips")),1),i("div",te,[e(v,{theme:"primary",onClick:a[18]||(a[18]=t=>h(0))},{default:s(()=>[n(u(l.t("pages.formStep.step4.reapply")),1)]),_:1}),e(v,{variant:"base",theme:"default",onClick:C},{default:s(()=>[n(u(l.t("pages.formStep.step4.check")),1)]),_:1})])]),_:1})],512),[[V,b.value===6]])])])}}}),pe=Y(se,[["__scopeId","data-v-821ac295"]]);export{pe as default};
