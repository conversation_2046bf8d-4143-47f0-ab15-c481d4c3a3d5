import{d as C,h as a,ab as O,ac as y,ad as d}from"./index-R826otwI.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function c(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){d(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var V={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M13.0039 2.00195L13.0018 3.08326C15.8386 3.56007 18 6.02763 18 9H22V22H2V9H6C6 6.02638 8.1632 3.558 11.0018 3.08266L11.0039 1.99805L13.0039 2.00195ZM8 9H16C16 6.79086 14.2091 5 12 5C9.79086 5 8 6.79086 8 9ZM10.998 5.99804H13.002V8.00195H10.998V5.99804ZM4 11V20H5V17C5 15.3431 6.34315 14 8 14C9.65685 14 11 15.3431 11 17V20H13V17C13 15.3431 14.3431 14 16 14C17.6569 14 19 15.3431 19 17V20H20V11H4ZM17 20V17C17 16.4477 16.5523 16 16 16C15.4477 16 15 16.4477 15 17V20H17ZM9 20V17C9 16.4477 8.55228 16 8 16C7.44772 16 7 16.4477 7 17V20H9Z"}}]},b=C({name:"Palace1Icon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:s}=O(t),p=a(()=>["t-icon","t-icon-palace-1",l.value]),u=a(()=>c(c({},s.value),r.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:f})}}));return()=>y(V,v.value)}});export{b as default};
