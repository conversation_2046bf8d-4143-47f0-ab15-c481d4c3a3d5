import{d as y,h as a,ab as C,ac as d,ad as O}from"./index-R826otwI.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){O(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var b={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M10.3171 4.64037C10.8542 4.10349 11.7252 4.10476 12.2608 4.64322L17.5146 9.92546 17.0702 8.82974C16.5106 7.45007 18.0758 6.1798 19.3108 7.01131 19.4992 7.13813 19.6565 7.30585 19.771 7.50194L22.1506 11.5767C23.7982 14.3981 23.3362 17.9757 21.0259 20.2859L20.1816 21.1303C17.3739 23.9379 12.8218 23.9379 10.0142 21.1303L4.9797 16.0946C4.44301 15.5578 4.44284 14.6877 4.97932 14.1506 5.51623 13.6132 6.38723 13.613 6.92441 14.1501L8.528 15.7537 9.05907 15.2227 5.36896 11.5326C4.8321 10.9957 4.83209 10.1253 5.36894 9.58839 5.90581 9.05149 6.77627 9.05148 7.31315 9.58836L11.0033 13.2785 11.5336 12.7481 6.47673 7.69128C5.93991 7.15446 5.93974 6.28415 6.47635 5.74712 7.01326 5.20979 7.88416 5.20962 8.42128 5.74674L13.4781 10.8036 14.0083 10.2734 10.3168 6.58191C9.78067 6.04574 9.78077 5.17641 10.3171 4.64037zM17.9793 1.54565L18.8026 2.11317C20.009 2.94467 21.0554 3.99115 21.8869 5.19753L22.4544 6.02091 20.8076 7.15589 20.2401 6.3325C19.5467 5.3264 18.6737 4.45335 17.6676 3.75989L16.8442 3.19237 17.9793 1.54565zM3.19237 16.8442L3.75989 17.6676C4.45335 18.6737 5.3264 19.5467 6.3325 20.2401L7.15589 20.8076 6.02091 22.4544 5.19753 21.8869C3.99115 21.0554 2.94467 20.009 2.11317 18.8026L1.54565 17.9793 3.19237 16.8442z"}}]},L=y({name:"WaveByeFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:c}=C(t),p=a(()=>["t-icon","t-icon-wave-bye-filled",l.value]),u=a(()=>s(s({},c.value),r.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:f})}}));return()=>d(b,v.value)}});export{L as default};
