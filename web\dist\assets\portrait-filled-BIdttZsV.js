import{d,h as a,ab as O,ac as y,ad as m}from"./index-R826otwI.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){m(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var H={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M4 4V9H2V2H9V4H4zM20 4V9H22V2H15V4H20zM4 15V20H9V22H2V15H4zM20 20V15H22V22H15V20H20zM12 7C13.6569 7 15 8.34315 15 10 15 11.1842 14.3139 12.2081 13.3175 12.696 15.16 13.2593 16.5 14.9731 16.5 17L16.5 17.9972H7.5L7.5 17C7.5 14.9731 8.84003 13.2593 10.6825 12.696 9.6861 12.2081 9 11.1842 9 10 9 8.34315 10.3431 7 12 7z"}}]},g=d({name:"PortraitFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:c}=O(t),p=a(()=>["t-icon","t-icon-portrait-filled",l.value]),u=a(()=>s(s({},c.value),r.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>y(H,f.value)}});export{g as default};
