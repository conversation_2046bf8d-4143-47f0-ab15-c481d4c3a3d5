import{d as B,ak as L,a0 as R,h as j,r as P,f as s,b as H,o as V,n as o,g as t,c as h,F as z,s as G,p as c,t as J,M as v,m as K,S as g,_ as Q}from"./index-R826otwI.js";const W={name:"AddModal"},X=B({...W,props:{visible:{type:Boolean},title:{},copyData:{}},emits:["update:visible","updataList"],setup(y,{emit:S}){const u=y,a=L(u,"copyData");R(()=>{a.value=u.copyData});const _=S,U=[{value:"PRD",label:"正式环境"},{value:"OTHER",label:"其他"}],m=j({get(){return u.visible},set(n){_("update:visible",n)}}),D={environmentName:[{required:!0,message:"环境名称不能为空"}],environmentType:[{required:!0,message:"环境类型不能为空"}],state:[{required:!0,message:"环境类型不能为空"}],serviceAddress:[{required:!0,message:"服务地址不能为空"}]},p=P(null),A=()=>{},k=({validateResult:n,firstError:e})=>{n===!0?v.success("提交成功"):(console.log("Validate Errors: ",e,n),v.warning(e))},w=async()=>{p.value.validate({showErrorMessage:!0}).then(n=>{if(!(n&&Object.keys(n).length)){const e={...a.value};u.title.includes("复制")&&delete e.id,K.run(u.title.includes("编辑")?g.publishUpdate:g.publishAdd,e).then(()=>{v.success("发布成功"),m.value=!1,f(),_("updataList")})}})},f=()=>{p.value.reset()},d=(n,{e})=>{e.preventDefault()},E=n=>{console.log("关闭弹窗，点击关闭按钮、按下ESC、点击蒙层等触发",n),f()},M=n=>{console.log("点击了确认按钮",n),w()};return(n,e)=>{const i=s("t-input"),r=s("t-form-item"),x=s("t-option"),I=s("t-select"),b=s("t-radio"),N=s("t-radio-group"),T=s("t-textarea"),C=s("t-form"),F=s("t-space"),O=s("t-dialog");return V(),H(O,{visible:m.value,"onUpdate:visible":e[8]||(e[8]=l=>m.value=l),header:n.title,width:"50%","confirm-on-enter":!0,"on-close":E,"on-confirm":M},{default:o(()=>[t(F,{direction:"vertical",style:{width:"100%"}},{default:o(()=>[t(C,{ref_key:"form",ref:p,layout:"inline","label-align":"left",rules:D,data:a.value,colon:!0,onReset:A,onSubmit:k},{default:o(()=>[t(r,{label:"环境名称",name:"environmentName"},{default:o(()=>[t(i,{modelValue:a.value.environmentName,"onUpdate:modelValue":e[0]||(e[0]=l=>a.value.environmentName=l),placeholder:"请输入环境名称",onEnter:d},null,8,["modelValue"])]),_:1}),t(r,{label:"环境类型",name:"environmentType"},{default:o(()=>[t(I,{modelValue:a.value.environmentType,"onUpdate:modelValue":e[1]||(e[1]=l=>a.value.environmentType=l),class:"demo-select-base",clearable:""},{default:o(()=>[(V(),h(z,null,G(U,(l,q)=>t(x,{key:q,value:l.value,label:l.label},{default:o(()=>[c(J(l.label),1)]),_:2},1032,["value","label"])),64))]),_:1},8,["modelValue"])]),_:1}),t(r,{label:"环境状态",name:"state"},{default:o(()=>[t(N,{modelValue:a.value.state,"onUpdate:modelValue":e[2]||(e[2]=l=>a.value.state=l)},{default:o(()=>[t(b,{value:1},{default:o(()=>e[9]||(e[9]=[c("启用",-1)])),_:1,__:[9]}),t(b,{value:0},{default:o(()=>e[10]||(e[10]=[c("停用",-1)])),_:1,__:[10]})]),_:1},8,["modelValue"])]),_:1}),t(r,{label:"服务地址",name:"serviceAddress"},{default:o(()=>[t(i,{modelValue:a.value.serviceAddress,"onUpdate:modelValue":e[3]||(e[3]=l=>a.value.serviceAddress=l),placeholder:"请输入服务地址",onEnter:d},null,8,["modelValue"])]),_:1}),t(r,{label:"服务ID",name:"serviceId"},{default:o(()=>[t(i,{modelValue:a.value.serviceId,"onUpdate:modelValue":e[4]||(e[4]=l=>a.value.serviceId=l),placeholder:"请输入服务ID",onEnter:d},null,8,["modelValue"])]),_:1}),t(r,{label:"服务Secret",name:"serviceSecret"},{default:o(()=>[t(i,{modelValue:a.value.serviceSecret,"onUpdate:modelValue":e[5]||(e[5]=l=>a.value.serviceSecret=l),placeholder:"请输入服务Secret",onEnter:d},null,8,["modelValue"])]),_:1}),t(r,{label:"标签",name:"tag"},{default:o(()=>[t(i,{modelValue:a.value.tag,"onUpdate:modelValue":e[6]||(e[6]=l=>a.value.tag=l),placeholder:"请输入标签",onEnter:d},null,8,["modelValue"])]),_:1}),t(r,{label:"描述",name:"description"},{default:o(()=>[t(T,{modelValue:a.value.description,"onUpdate:modelValue":e[7]||(e[7]=l=>a.value.description=l),placeholder:"请输入描述",clearable:""},null,8,["modelValue"])]),_:1})]),_:1},8,["data"])]),_:1})]),_:1},8,["visible","header"])}}}),Z=Q(X,[["__scopeId","data-v-d5c02633"]]);export{Z as default};
