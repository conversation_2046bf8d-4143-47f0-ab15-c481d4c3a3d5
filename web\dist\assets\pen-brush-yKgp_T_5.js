import{d as O,h as a,ab as y,ac as d,ad as b}from"./index-R826otwI.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){b(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var h={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M1.99902 22.0003H5.62161C6.41726 22.0003 7.18032 21.6842 7.74293 21.1216L22.5311 6.33349L17.6658 1.46826L2.8777 16.2564C2.31509 16.819 1.99902 17.5821 1.99902 18.3777L1.99902 22.0003ZM3.99902 20.0003L3.99902 18.3777C3.99902 18.1125 4.10438 17.8581 4.29192 17.6706L6.45004 15.5125L8.48684 17.5493L6.32872 19.7074C6.14118 19.8949 5.88683 20.0003 5.62161 20.0003H3.99902ZM9.90105 16.1351L7.86425 14.0983L17.6658 4.29669L19.7026 6.33349L9.90105 16.1351Z"}}]},g=O({name:"PenBrushIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:o,style:c}=y(t),p=a(()=>["t-icon","t-icon-pen-brush",o.value]),u=a(()=>s(s({},c.value),r.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:f})}}));return()=>d(h,v.value)}});export{g as default};
