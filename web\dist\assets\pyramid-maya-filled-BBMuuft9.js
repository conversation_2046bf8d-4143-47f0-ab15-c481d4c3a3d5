import{d as y,h as n,ab as d,ac as H,ad as m}from"./index-R826otwI.js";function o(e,a){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);a&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var a=1;a<arguments.length;a++){var r=arguments[a]!=null?arguments[a]:{};a%2?o(Object(r),!0).forEach(function(t){m(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var O={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M6 2H18V4H17V8H7V4H6V2zM11 4.49805V6.50195H13.0039V4.49805H11zM5 10H8V23H2V19H3V16H4L4 13H5V10zM14 10H10V23H14V10zM19 10H16V23H22V19H21V16H20V13H19V10z"}}]},b=y({name:"PyramidMayaFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,a){var{attrs:r}=a,t=n(()=>e.size),{className:l,style:c}=d(t),p=n(()=>["t-icon","t-icon-pyramid-maya-filled",l.value]),u=n(()=>s(s({},c.value),r.style)),f=n(()=>({class:p.value,style:u.value,onClick:v=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:v})}}));return()=>H(O,f.value)}});export{b as default};
