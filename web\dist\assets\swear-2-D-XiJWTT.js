import{d as f,h as a,ab as O,ac as y,ad as d}from"./index-R826otwI.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){d(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3ZM1 12C1 5.92487 5.92487 1 12 1C18.0751 1 23 5.92487 23 12C23 18.0751 18.0751 23 12 23C5.92487 23 1 18.0751 1 12ZM6.76874 8.13397L10.2328 10.134L9.23284 11.866L5.76874 9.86603L6.76874 8.13397ZM18.2328 9.86603L14.7687 11.866L13.7687 10.134L17.2328 8.13397L18.2328 9.86603ZM12 14C11.6444 14 11 14.4518 11 15.5C11 16.5482 11.6444 17 12 17C12.3556 17 13 16.5482 13 15.5C13 14.4518 12.3556 14 12 14ZM9 15.5C9 13.7868 10.1464 12 12 12C13.8536 12 15 13.7868 15 15.5C15 17.2132 13.8536 19 12 19C10.1464 19 9 17.2132 9 15.5Z"}}]},g=f({name:"Swear2Icon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:o,style:c}=O(t),p=a(()=>["t-icon","t-icon-swear-2",o.value]),u=a(()=>s(s({},c.value),r.style)),C=a(()=>({class:p.value,style:u.value,onClick:v=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:v})}}));return()=>y(m,C.value)}});export{g as default};
