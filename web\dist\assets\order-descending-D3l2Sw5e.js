import{d as f,h as a,ab as H,ac as O,ad as y}from"./index-CTFw1yDv.js";function l(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?l(Object(r),!0).forEach(function(t){y(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var g={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M2 4H3H14H15V6H14H3H2V4ZM2 11H3H14H15V13H14H3H2V11ZM3 18H2V20H3H12H13V18H12H3ZM19 21.4142L19.7071 20.7071L22.7071 17.7071L23.4142 17L22 15.5858L21.2929 16.2929L20 17.5858V15.8261V14.7826V5V4H18V5V14.7826V15.8261V17.5858L16.7071 16.2929L16 15.5858L14.5858 17L15.2929 17.7071L18.2929 20.7071L19 21.4142Z"}}]},m=f({name:"OrderDescendingIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:o,style:c}=H(t),p=a(()=>["t-icon","t-icon-order-descending",o.value]),u=a(()=>s(s({},c.value),r.style)),d=a(()=>({class:p.value,style:u.value,onClick:v=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:v})}}));return()=>O(g,d.value)}});export{m as default};
