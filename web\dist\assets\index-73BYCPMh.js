import{R as r}from"./index-BiCzxYwG.js";import{d as a,f as n,b as p,o as l,n as o,g as u,p as i,t as _}from"./index-R826otwI.js";const f={name:"Result500"},b=a({...f,setup(m){return(t,e)=>{const s=n("t-button");return l(),p(r,{title:"500 Internal Server Error",type:"500",tip:t.t("pages.result.500.subtitle")},{default:o(()=>[u(s,{onClick:e[0]||(e[0]=()=>t.$router.push("/"))},{default:o(()=>[i(_(t.t("pages.result.500.back")),1)]),_:1})]),_:1},8,["tip"])}}});export{b as default};
