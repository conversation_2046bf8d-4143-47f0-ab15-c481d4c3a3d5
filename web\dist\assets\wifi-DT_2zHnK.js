import{d as O,h as a,ab as y,ac as L,ad as d}from"./index-R826otwI.js";function l(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?l(Object(r),!0).forEach(function(t){d(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M21.1926 9.8076C16.1158 4.73082 7.88466 4.73078 2.80784 9.8076L2.10074 10.5147L0.686523 9.10049L1.39363 8.39339C7.2515 2.53551 16.749 2.53556 22.6068 8.39339L23.3139 9.10049L21.8997 10.5147L21.1926 9.8076ZM16.5964 14.4038C14.058 11.8654 9.94245 11.8654 7.40404 14.4038L6.69693 15.1109L5.28271 13.6967L5.98982 12.9896C9.30929 9.67011 14.6912 9.67016 18.0106 12.9896L18.7177 13.6967L17.3035 15.1109L16.5964 14.4038ZM10.586 17.5858C11.3671 16.8047 12.6335 16.8048 13.4145 17.5858L14.1216 18.2929L12.0003 20.4142L9.87894 18.2929L10.586 17.5858Z"}}]},g=O({name:"WifiIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:i,style:c}=y(t),p=a(()=>["t-icon","t-icon-wifi",i.value]),u=a(()=>s(s({},c.value),r.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>L(m,f.value)}});export{g as default};
