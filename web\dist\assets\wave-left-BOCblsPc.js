import{d as v,h as a,ab as f,ac as O,ad as y}from"./index-R826otwI.js";function l(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(C){return Object.getOwnPropertyDescriptor(e,C).enumerable})),t.push.apply(t,r)}return t}function i(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?l(Object(t),!0).forEach(function(r){y(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):l(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var d={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M7.15613 3.19218L6.33275 3.75966C5.32665 4.45308 4.45359 5.3261 3.76014 6.33217L3.19262 7.15553L1.5459 6.02049L2.11342 5.19713C2.94492 3.99079 3.99139 2.94435 5.19777 2.1129L6.02116 1.54541L7.15613 3.19218ZM11.4561 6.87746C11.3047 6.72607 11.0592 6.72607 10.9079 6.87746C10.7566 7.02873 10.7565 7.27392 10.9075 7.42534C10.9074 7.42522 10.9076 7.42546 10.9075 7.42534L16.1445 12.6623L14.7303 14.0765L8.12683 7.4731C7.97543 7.32171 7.72998 7.32171 7.57858 7.4731C7.42719 7.6245 7.42719 7.86996 7.57858 8.02135L14.182 14.6248L12.7678 16.039L7.53119 10.8024C7.53106 10.8023 7.53131 10.8025 7.53119 10.8024C7.37976 10.6513 7.13421 10.6511 6.98294 10.8024C6.83155 10.9538 6.83155 11.1992 6.98294 11.3506L9.06682 13.4345C9.06712 13.4348 9.06742 13.4351 9.06773 13.4354L12.2196 16.5872L10.8053 18.0015L7.65351 14.8496C7.5029 14.699 7.2587 14.699 7.10808 14.8496C6.95746 15.0002 6.95746 15.2444 7.10808 15.3951L11.4285 19.7155C13.4551 21.7421 16.7409 21.7421 18.7675 19.7155L19.6118 18.8712C21.2794 17.2036 21.6129 14.6212 20.4237 12.5847L18.1974 8.77235C18.1543 8.6985 18.0571 8.67758 17.9874 8.72716C17.9378 8.76239 17.9154 8.82469 17.9312 8.88339L18.5999 11.379C18.6286 11.4864 18.6432 11.6124 18.6281 11.7493C18.6236 11.7904 18.6132 11.8624 18.5854 11.9484C18.5631 12.0173 18.4977 12.1988 18.3238 12.3645C18.1009 12.577 17.767 12.6942 17.4182 12.6172C17.1476 12.5575 16.9833 12.4047 16.9263 12.3476L11.4561 6.87746ZM5.98851 13.1846L5.56873 12.7648C4.63629 11.8324 4.63628 10.3206 5.56873 9.38816C5.67104 9.28585 5.78033 9.19476 5.89507 9.1149C5.24279 8.18357 5.33255 6.89071 6.16437 6.05889C6.99619 5.22707 8.28905 5.13731 9.22038 5.78959C9.30024 5.67485 9.39133 5.56556 9.49364 5.46325C10.4261 4.53081 11.9379 4.53081 12.8703 5.46325L15.9479 8.54084C16.0298 7.97135 16.3394 7.4451 16.8279 7.09751C17.8554 6.36655 19.2886 6.67494 19.9245 7.76379L22.1508 11.5762C23.7983 14.3976 23.3363 17.9751 21.0261 20.2854L20.1818 21.1297C17.3741 23.9374 12.822 23.9374 10.0143 21.1297L5.69386 16.8093C4.7622 15.8776 4.7622 14.3671 5.69386 13.4354C5.78671 13.3426 5.88531 13.259 5.98851 13.1846Z"}}]},b=v({name:"WaveLeftIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:C,style:s}=f(r),c=a(()=>["t-icon","t-icon-wave-left",C.value]),p=a(()=>i(i({},s.value),t.style)),L=a(()=>({class:c.value,style:p.value,onClick:u=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:u})}}));return()=>O(d,L.value)}});export{b as default};
