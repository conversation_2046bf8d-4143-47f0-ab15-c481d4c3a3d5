import{d as O,h as a,ab as y,ac as d,ad as V}from"./index-CTFw1yDv.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function c(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){V(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M6 1.69824L10 5.03158V8.99995H14V5.03158L18 1.69824L22 5.03158V22H2V5.03158L6 1.69824ZM16 8.99995H20V5.96833L18 4.30166L16 5.96833V8.99995ZM20 11H4V20H9V17C9 15.3431 10.3431 14 12 14C13.6569 14 15 15.3431 15 17V20H20V11ZM13 20V17C13 16.4477 12.5523 16 12 16C11.4477 16 11 16.4477 11 17V20H13ZM4 8.99995H8V5.96833L6 4.30166L4 5.96833V8.99995Z"}}]},g=O({name:"Palace4Icon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:s}=y(t),p=a(()=>["t-icon","t-icon-palace-4",l.value]),u=a(()=>c(c({},s.value),r.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:f})}}));return()=>d(m,v.value)}});export{g as default};
