import{d as O,h as a,ab as d,ac as m,ad as y}from"./index-R826otwI.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){y(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var b={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M4.48959 16.5104C7.56978 19.5906 12.4258 19.8126 15.762 17.1765L21.1064 22.5209L22.5206 21.1067L17.1763 15.7623C19.8126 12.4261 19.5907 7.56987 16.5104 4.48959C13.191 1.17014 7.80905 1.17014 4.48959 4.48959C1.17014 7.80905 1.17014 13.191 4.48959 16.5104ZM6.5 9.5L14.5 9.5V11.5H6.5V9.5Z"}}]},C=O({name:"ZoomOutFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:o,style:c}=d(r),p=a(()=>["t-icon","t-icon-zoom-out-filled",o.value]),u=a(()=>s(s({},c.value),t.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:v})}}));return()=>m(b,f.value)}});export{C as default};
