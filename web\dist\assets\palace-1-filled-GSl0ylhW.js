import{d,h as a,ab as C,ac as O,ad as y}from"./index-CTFw1yDv.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function c(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){y(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var g={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M13.0039 2.00195L13.0018 3.08326C15.8386 3.56007 18 6.02763 18 9L22 9V22H20V17.5C20 15.567 18.433 14 16.5 14C14.567 14 13 15.567 13 17.5V22H11V17.5C11 15.567 9.433 14 7.5 14C5.567 14 4 15.567 4 17.5V22H2V9L6 9C6 6.02638 8.1632 3.558 11.0018 3.08266L11.0039 1.99805L13.0039 2.00195ZM10.998 5.99805V8.00195H13.002V5.99805H10.998Z"}},{tag:"path",attrs:{fill:"currentColor",d:"M7.5 16C6.67157 16 6 16.6716 6 17.5V22H9V17.5C9 16.6716 8.32843 16 7.5 16zM16.5 16C15.6716 16 15 16.6716 15 17.5V22H18V17.5C18 16.6716 17.3284 16 16.5 16z"}}]},b=d({name:"Palace1FilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:l,style:s}=C(r),p=a(()=>["t-icon","t-icon-palace-1-filled",l.value]),u=a(()=>c(c({},s.value),t.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>O(g,f.value)}});export{b as default};
