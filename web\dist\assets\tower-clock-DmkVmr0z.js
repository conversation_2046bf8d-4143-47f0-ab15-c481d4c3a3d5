import{d as C,h as a,ab as O,ac as y,ad as d}from"./index-CTFw1yDv.js";function c(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function i(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?c(Object(r),!0).forEach(function(t){d(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var V={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M12 0.932129L17.2875 2.91492L16.5852 4.78758L16 4.56813V5.00013H19V17.0001H17V23.0001H7V17.0001H5V5.00013H8V4.56813L7.41479 4.78758L6.71255 2.91492L12 0.932129ZM10 3.81813V5.00013H14V3.81813L12 3.06813L10 3.81813ZM9 17.0001V21.0001H15V17.0001H13V20.0001H11V17.0001H9ZM17 15.0001V7.00013H7V15.0001H17ZM12 10.0001C11.4477 10.0001 11 10.4478 11 11.0001C11 11.5524 11.4477 12.0001 12 12.0001C12.5523 12.0001 13 11.5524 13 11.0001C13 10.4478 12.5523 10.0001 12 10.0001ZM9 11.0001C9 9.34328 10.3431 8.00013 12 8.00013C13.6569 8.00013 15 9.34328 15 11.0001C15 12.657 13.6569 14.0001 12 14.0001C10.3431 14.0001 9 12.657 9 11.0001Z"}}]},b=C({name:"TowerClockIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:o,style:s}=O(t),p=a(()=>["t-icon","t-icon-tower-clock",o.value]),u=a(()=>i(i({},s.value),r.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:f})}}));return()=>y(V,v.value)}});export{b as default};
